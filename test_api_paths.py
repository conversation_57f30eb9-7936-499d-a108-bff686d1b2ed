#!/usr/bin/env python3
"""
简单的API路径测试脚本
验证前后端对接清单中的API路径是否正确工作
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_api_paths():
    """测试统一后的API路径"""
    
    print("🔍 测试前后端对接清单中的API路径...")
    
    # 测试健康检查
    print("\n1. 测试健康检查 GET /api/health-check/")
    try:
        response = requests.get(f"{BASE_URL}/api/health-check/")
        print(f"   状态码: {response.status_code}")
        if response.status_code in [200, 401, 403]:
            print("   ✅ 路径正确")
        else:
            print("   ❌ 路径可能有问题")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 测试用户注册路径
    print("\n2. 测试用户注册 POST /api/users/register/")
    try:
        response = requests.post(f"{BASE_URL}/api/users/register/", 
                               json={"username": "test", "password": "test"})
        print(f"   状态码: {response.status_code}")
        if response.status_code in [200, 201, 400]:
            print("   ✅ 路径正确")
        else:
            print("   ❌ 路径可能有问题")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 测试用户登录路径
    print("\n3. 测试用户登录 POST /api/users/login/")
    try:
        response = requests.post(f"{BASE_URL}/api/users/login/", 
                               json={"username": "test", "password": "test"})
        print(f"   状态码: {response.status_code}")
        if response.status_code in [200, 201, 400, 401]:
            print("   ✅ 路径正确")
        else:
            print("   ❌ 路径可能有问题")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 测试token刷新路径
    print("\n4. 测试token刷新 POST /api/users/token/refresh/")
    try:
        response = requests.post(f"{BASE_URL}/api/users/token/refresh/", 
                               json={"refresh": "dummy_token"})
        print(f"   状态码: {response.status_code}")
        if response.status_code in [200, 400, 401]:
            print("   ✅ 路径正确")
        else:
            print("   ❌ 路径可能有问题")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 测试创建房间路径
    print("\n5. 测试创建房间 POST /api/rooms/")
    try:
        response = requests.post(f"{BASE_URL}/api/rooms/", 
                               json={"template_id": "1"})
        print(f"   状态码: {response.status_code}")
        if response.status_code in [200, 201, 400, 401, 403]:
            print("   ✅ 路径正确")
        else:
            print("   ❌ 路径可能有问题")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 测试模板列表路径
    print("\n6. 测试模板列表 GET /api/templates/")
    try:
        response = requests.get(f"{BASE_URL}/api/templates/")
        print(f"   状态码: {response.status_code}")
        if response.status_code in [200, 401, 403]:
            print("   ✅ 路径正确")
        else:
            print("   ❌ 路径可能有问题")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 测试日历数据路径
    print("\n7. 测试日历数据 GET /api/calendar/")
    try:
        response = requests.get(f"{BASE_URL}/api/calendar/")
        print(f"   状态码: {response.status_code}")
        if response.status_code in [200, 401, 403]:
            print("   ✅ 路径正确")
        else:
            print("   ❌ 路径可能有问题")
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    print("\n🎯 API路径测试完成！")
    print("注意：401/403状态码表示需要认证，这是正常的")
    print("404状态码表示路径不存在，需要检查")

if __name__ == "__main__":
    test_api_paths()
