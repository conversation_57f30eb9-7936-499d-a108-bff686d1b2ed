"""
测试房间时间轴功能

验证房间环节获取API和时间轴相关功能的正确性
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from django.utils import timezone

from core.models import Room, RoomState, RoomEventStep, RoomParticipant
from events.models import EventTemplate, EventStep

User = get_user_model()


class RoomStepsAPITest(APITestCase):
    """测试房间环节API"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.other_user = User.objects.create_user(
            username='otheruser',
            password='testpass123'
        )
        
        # 创建模板
        self.template = EventTemplate.objects.create(
            name='测试模板',
            description='测试用模板',
            creator=self.user
        )
        
        # 创建模板步骤
        EventStep.objects.create(
            template=self.template,
            order=1,
            name='第一个环节',
            step_type=EventStep.STEP_FREE_CHAT,
            duration=300
        )
        
        EventStep.objects.create(
            template=self.template,
            order=2,
            name='第二个环节',
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=600
        )
        
        # 创建房间
        self.room = Room.objects.create(
            room_code='TEST01',
            name='测试房间',
            host=self.user,
            event_template=self.template,
            status=RoomState.READY
        )
        
        # 复制模板步骤到房间
        from core.models import TemplateManager
        TemplateManager.copy_template_steps_to_room(self.template, 'user', self.room)
        
        # 添加用户到房间
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user,
            joined_at=timezone.now()
        )
        
        # 登录用户
        self.client.force_authenticate(user=self.user)
    
    def test_get_room_steps_success(self):
        """测试成功获取房间环节"""
        response = self.client.get(f'/api/rooms/{self.room.room_code}/steps/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('room_code', data)
        self.assertIn('room_status', data)
        self.assertIn('current_step_order', data)
        self.assertIn('total_steps', data)
        self.assertIn('steps', data)
        self.assertIn('is_host', data)
        
        # 验证基本信息
        self.assertEqual(data['room_code'], self.room.room_code)
        self.assertEqual(data['room_status'], RoomState.READY)
        self.assertEqual(data['current_step_order'], 0)
        self.assertEqual(data['total_steps'], 2)
        self.assertTrue(data['is_host'])
        
        # 验证环节信息
        steps = data['steps']
        self.assertEqual(len(steps), 2)
        
        first_step = steps[0]
        self.assertEqual(first_step['order'], 1)
        self.assertEqual(first_step['name'], '第一个环节')
        self.assertEqual(first_step['step_type'], EventStep.STEP_FREE_CHAT)
        self.assertEqual(first_step['duration'], 300)
        self.assertFalse(first_step['is_completed'])
        self.assertFalse(first_step['is_current'])
        self.assertIsNone(first_step['completed_at'])
        
        second_step = steps[1]
        self.assertEqual(second_step['order'], 2)
        self.assertEqual(second_step['name'], '第二个环节')
        self.assertEqual(second_step['step_type'], EventStep.STEP_GAME_PICTIONARY)
        self.assertEqual(second_step['duration'], 600)
        self.assertFalse(second_step['is_completed'])
        self.assertFalse(second_step['is_current'])
    
    def test_get_room_steps_with_current_step(self):
        """测试获取有当前环节的房间环节"""
        # 设置当前环节
        first_step = self.room.event_steps.first()
        self.room.current_event_step = first_step
        self.room.current_step_order = 1
        self.room.status = RoomState.IN_PROGRESS
        self.room.save()
        
        response = self.client.get(f'/api/rooms/{self.room.room_code}/steps/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertEqual(data['room_status'], RoomState.IN_PROGRESS)
        self.assertEqual(data['current_step_order'], 1)
        
        # 验证当前环节标记
        steps = data['steps']
        first_step_data = steps[0]
        second_step_data = steps[1]
        
        self.assertTrue(first_step_data['is_current'])
        self.assertFalse(second_step_data['is_current'])
    
    def test_get_room_steps_with_completed_step(self):
        """测试获取有已完成环节的房间环节"""
        # 标记第一个环节为已完成
        first_step = self.room.event_steps.first()
        first_step.is_completed = True
        first_step.completed_at = timezone.now()
        first_step.save()
        
        response = self.client.get(f'/api/rooms/{self.room.room_code}/steps/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        steps = data['steps']
        first_step_data = steps[0]
        
        self.assertTrue(first_step_data['is_completed'])
        self.assertIsNotNone(first_step_data['completed_at'])
    
    def test_get_room_steps_nonexistent_room(self):
        """测试获取不存在房间的环节"""
        response = self.client.get('/api/rooms/NONEXISTENT/steps/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['error'], '房间不存在')
    
    def test_get_room_steps_not_in_room(self):
        """测试用户不在房间中时获取环节"""
        # 使用其他用户
        self.client.force_authenticate(user=self.other_user)
        
        response = self.client.get(f'/api/rooms/{self.room.room_code}/steps/')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['error'], '您不在此房间中')
    
    def test_get_room_steps_participant_vs_host(self):
        """测试参与者和房主获取环节的区别"""
        # 添加其他用户到房间作为参与者
        RoomParticipant.objects.create(
            room=self.room,
            user=self.other_user,
            joined_at=timezone.now()
        )
        
        # 房主获取环节
        response = self.client.get(f'/api/rooms/{self.room.room_code}/steps/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        host_data = response.json()
        self.assertTrue(host_data['is_host'])
        
        # 参与者获取环节
        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(f'/api/rooms/{self.room.room_code}/steps/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        participant_data = response.json()
        self.assertFalse(participant_data['is_host'])
        
        # 其他信息应该相同
        self.assertEqual(host_data['room_code'], participant_data['room_code'])
        self.assertEqual(host_data['total_steps'], participant_data['total_steps'])
        self.assertEqual(len(host_data['steps']), len(participant_data['steps']))
    
    def test_get_room_steps_unauthenticated(self):
        """测试未认证用户获取环节"""
        self.client.force_authenticate(user=None)
        
        response = self.client.get(f'/api/rooms/{self.room.room_code}/steps/')
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])


class RoomStepsBusinessLogicTest(TestCase):
    """测试房间环节业务逻辑"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.room = Room.objects.create(
            room_code='TEST01',
            name='测试房间',
            host=self.user,
            status=RoomState.READY
        )
    
    def test_room_steps_ordering(self):
        """测试房间环节排序"""
        # 创建多个环节（不按顺序）
        step3 = RoomEventStep.objects.create(
            room=self.room,
            order=3,
            name='第三个环节',
            step_type='FREE_CHAT',
            duration=300
        )
        
        step1 = RoomEventStep.objects.create(
            room=self.room,
            order=1,
            name='第一个环节',
            step_type='FREE_CHAT',
            duration=300
        )
        
        step2 = RoomEventStep.objects.create(
            room=self.room,
            order=2,
            name='第二个环节',
            step_type='FREE_CHAT',
            duration=300
        )
        
        # 获取排序后的环节
        steps = self.room.event_steps.all().order_by('order')
        step_names = [step.name for step in steps]
        
        self.assertEqual(step_names, ['第一个环节', '第二个环节', '第三个环节'])
    
    def test_room_current_step_tracking(self):
        """测试房间当前环节跟踪"""
        # 创建环节
        step1 = RoomEventStep.objects.create(
            room=self.room,
            order=1,
            name='第一个环节',
            step_type='FREE_CHAT',
            duration=300
        )
        
        step2 = RoomEventStep.objects.create(
            room=self.room,
            order=2,
            name='第二个环节',
            step_type='FREE_CHAT',
            duration=300
        )
        
        # 设置当前环节
        self.room.current_event_step = step1
        self.room.current_step_order = 1
        self.room.save()
        
        # 验证当前环节
        self.assertEqual(self.room.current_event_step, step1)
        self.assertEqual(self.room.current_step_order, 1)
        
        # 推进到下一环节
        self.room.current_event_step = step2
        self.room.current_step_order = 2
        self.room.save()
        
        # 验证推进结果
        self.assertEqual(self.room.current_event_step, step2)
        self.assertEqual(self.room.current_step_order, 2)
    
    def test_room_step_completion_tracking(self):
        """测试房间环节完成状态跟踪"""
        step = RoomEventStep.objects.create(
            room=self.room,
            order=1,
            name='测试环节',
            step_type='FREE_CHAT',
            duration=300
        )
        
        # 初始状态
        self.assertFalse(step.is_completed)
        self.assertIsNone(step.completed_at)
        
        # 标记为完成
        step.is_completed = True
        step.completed_at = timezone.now()
        step.save()
        
        # 验证完成状态
        step.refresh_from_db()
        self.assertTrue(step.is_completed)
        self.assertIsNotNone(step.completed_at)
