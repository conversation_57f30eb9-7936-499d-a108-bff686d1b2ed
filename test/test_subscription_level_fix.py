"""
测试订阅等级获取修复

验证以下修复：
1. 登录后JWT token包含正确的订阅信息
2. AuthContext正确解码和存储订阅等级
3. 前端组件能正确显示用户订阅状态
"""

import pytest
import json
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.test import APIClient

User = get_user_model()


class TestSubscriptionLevelFix(TestCase):
    """测试订阅等级获取修复"""

    def setUp(self):
        """设置测试数据"""
        self.client = APIClient()
        
        # 创建不同订阅等级的用户
        self.free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level='Free'
        )
        
        self.pro_user = User.objects.create_user(
            username='prouser',
            password='testpass123',
            subscription_level='Pro'
        )
        
        self.max_user = User.objects.create_user(
            username='maxuser',
            password='testpass123',
            subscription_level='Max'
        )

    def test_login_returns_correct_subscription_level(self):
        """测试登录返回正确的订阅等级"""
        # 测试Free用户
        response = self.client.post('/api/users/login/', {
            'username': 'freeuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('access', data)
        
        # 解码token验证订阅信息
        from jwt import decode
        from django.conf import settings
        
        decoded = decode(data['access'], settings.SECRET_KEY, algorithms=['HS256'])
        self.assertEqual(decoded['username'], 'freeuser')
        self.assertEqual(decoded['subscription_level'], 'Free')

        # 测试Pro用户
        response = self.client.post('/api/users/login/', {
            'username': 'prouser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        decoded = decode(data['access'], settings.SECRET_KEY, algorithms=['HS256'])
        self.assertEqual(decoded['subscription_level'], 'Pro')

        # 测试Max用户
        response = self.client.post('/api/users/login/', {
            'username': 'maxuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        decoded = decode(data['access'], settings.SECRET_KEY, algorithms=['HS256'])
        self.assertEqual(decoded['subscription_level'], 'Max')

    def test_subscription_api_returns_correct_info(self):
        """测试订阅API返回正确信息"""
        # 登录获取token
        response = self.client.post('/api/users/login/', {
            'username': 'prouser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        token = response.json()['access']
        
        # 调用订阅API
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.get('/api/subscription/')
        
        if response.status_code == 200:
            data = response.json()
            self.assertEqual(data['current_level'], 'Pro')
            self.assertEqual(data['username'], 'prouser')
        else:
            # 如果API不存在，跳过测试
            self.skipTest('Subscription API not implemented')

    def test_user_model_subscription_level(self):
        """测试用户模型的订阅等级"""
        self.assertEqual(self.free_user.subscription_level, 'Free')
        self.assertEqual(self.pro_user.subscription_level, 'Pro')
        self.assertEqual(self.max_user.subscription_level, 'Max')

    def test_subscription_level_update(self):
        """测试订阅等级更新"""
        # 更新用户订阅等级
        self.free_user.subscription_level = 'Pro'
        self.free_user.save()
        
        # 重新从数据库获取
        self.free_user.refresh_from_db()
        self.assertEqual(self.free_user.subscription_level, 'Pro')
        
        # 测试登录后token包含更新的订阅等级
        response = self.client.post('/api/users/login/', {
            'username': 'freeuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        from jwt import decode
        from django.conf import settings
        
        decoded = decode(data['access'], settings.SECRET_KEY, algorithms=['HS256'])
        self.assertEqual(decoded['subscription_level'], 'Pro')

    def test_jwt_token_structure(self):
        """测试JWT token结构"""
        # 创建token
        refresh = RefreshToken.for_user(self.pro_user)
        access_token = refresh.access_token
        
        # 添加自定义声明
        access_token['username'] = self.pro_user.username
        access_token['subscription_level'] = self.pro_user.subscription_level
        
        # 验证token结构
        token_string = str(access_token)
        
        from jwt import decode
        from django.conf import settings
        
        decoded = decode(token_string, settings.SECRET_KEY, algorithms=['HS256'])
        
        # 验证必要字段
        self.assertIn('user_id', decoded)
        self.assertIn('username', decoded)
        self.assertIn('subscription_level', decoded)
        self.assertIn('exp', decoded)
        self.assertIn('iat', decoded)
        
        # 验证值
        self.assertEqual(decoded['username'], 'prouser')
        self.assertEqual(decoded['subscription_level'], 'Pro')

    def test_subscription_level_choices(self):
        """测试订阅等级选择"""
        valid_levels = ['Free', 'Pro', 'Max']
        
        for level in valid_levels:
            user = User.objects.create_user(
                username=f'test_{level.lower()}',
                password='testpass123',
                subscription_level=level
            )
            self.assertEqual(user.subscription_level, level)

    def test_default_subscription_level(self):
        """测试默认订阅等级"""
        user = User.objects.create_user(
            username='defaultuser',
            password='testpass123'
            # 不指定subscription_level，应该使用默认值
        )
        self.assertEqual(user.subscription_level, 'Free')


if __name__ == '__main__':
    pytest.main([__file__])
