"""
测试你画我猜游戏重构功能

验证：
1. 每局时长可以由房主设定
2. 当剩余时间不足时，游戏会结束
3. 不再受整个环节时长的严格限制
4. 最大局数限制正常工作
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from unittest.mock import patch, MagicMock

from core.models import Room, RoomState, RoomParticipant
from events.models import EventTemplate, EventStep
from core.event_handlers.PictionaryEventHandler import PictionaryEventHandler

User = get_user_model()


class TestPictionaryRefactor(TestCase):
    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.user1 = User.objects.create_user(
            username='user1',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.user2 = User.objects.create_user(
            username='user2',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        # 创建测试模板和步骤
        self.template = EventTemplate.objects.create(
            name='测试模板',
            description='用于测试的模板',
            creator=self.user1
        )
        
        # 创建你画我猜步骤，配置每局90秒，最多5局
        self.step = EventStep.objects.create(
            template=self.template,
            name='你画我猜测试',
            step_type='GAME_PICTIONARY',
            order=1,
            duration=600,  # 10分钟总时长
            configuration={
                'round_duration': 90,  # 每局90秒
                'max_rounds': 5,  # 最多5局
                'difficulty': 'medium'
            }
        )
        
        # 创建房间
        self.room = Room.objects.create(
            room_code='TEST001',
            host=self.user1,
            event_template=self.template,
            status=RoomState.READY,
            duration_hours=1
        )
        
        # 添加参与者
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user1,
            role=RoomParticipant.ROLE_HOST
        )
        
        RoomParticipant.objects.create(
            room=self.room,
            user=self.user2,
            role=RoomParticipant.ROLE_PARTICIPANT
        )

    def test_pictionary_handler_initialization(self):
        """测试你画我猜处理器的初始化"""
        mock_consumer = MagicMock()
        handler = PictionaryEventHandler('TEST001', mock_consumer)
        self.assertEqual(handler.room_code, 'TEST001')

    @patch('core.event_handlers.PictionaryEventHandler.PictionaryEventHandler._setup_new_round')
    async def test_start_step_with_custom_config(self, mock_setup):
        """测试使用自定义配置启动游戏"""
        # 模拟_setup_new_round的返回值
        mock_setup.return_value = {
            "game_instance": MagicMock(),
            "drawer_username": "user1",
            "word": "测试词汇",
            "room_status": RoomState.IN_PROGRESS
        }
        
        mock_consumer = MagicMock()
        handler = PictionaryEventHandler('TEST001', mock_consumer)
        result, error = await handler.start_step(self.room, self.step)
        
        self.assertIsNone(error)
        self.assertIsNotNone(result)
        
        # 验证配置被正确读取
        self.assertEqual(result['round_duration'], 90)
        self.assertEqual(result['max_rounds'], 5)
        self.assertEqual(result['step_duration'], 600)
        self.assertEqual(result['current_round'], 1)

    def test_has_enough_time_for_next_round(self):
        """测试时间检查逻辑"""
        mock_consumer = MagicMock()
        handler = PictionaryEventHandler('TEST001', mock_consumer)
        
        # 设置游戏状态
        handler.step_start_time = timezone.now()
        handler.step_duration = 600  # 10分钟
        handler.round_duration = 90  # 90秒每局
        
        # 刚开始时应该有足够时间
        self.assertTrue(handler._has_enough_time_for_next_round())
        
        # 模拟时间过去了7分钟（420秒）
        handler.step_start_time = timezone.now() - timedelta(seconds=420)
        # 剩余时间180秒，应该足够开始新一局（90+30=120秒）
        self.assertTrue(handler._has_enough_time_for_next_round())
        
        # 模拟时间过去了9分钟（540秒）
        handler.step_start_time = timezone.now() - timedelta(seconds=540)
        # 剩余时间60秒，不足以开始新一局（需要90+30=120秒）
        self.assertFalse(handler._has_enough_time_for_next_round())

    def test_has_enough_time_without_time_limit(self):
        """测试没有时间限制时的行为"""
        mock_consumer = MagicMock()
        handler = PictionaryEventHandler('TEST001', mock_consumer)
        
        # 没有设置时间相关属性时，应该允许继续
        self.assertTrue(handler._has_enough_time_for_next_round())

    def test_max_rounds_limit(self):
        """测试最大局数限制"""
        mock_consumer = MagicMock()
        handler = PictionaryEventHandler('TEST001', mock_consumer)
        
        # 设置游戏状态
        handler.current_round = 5
        handler.max_rounds = 5
        handler.step_start_time = timezone.now()
        handler.step_duration = 3600  # 1小时，足够的时间
        handler.round_duration = 90
        
        # 已经到达最大局数，即使有时间也不应该继续
        handler.current_round += 1  # 模拟进入第6局
        has_more_rounds = (
            handler.current_round <= handler.max_rounds and 
            handler._has_enough_time_for_next_round()
        )
        self.assertFalse(has_more_rounds)

    def test_time_and_rounds_combined_limit(self):
        """测试时间和局数的组合限制"""
        mock_consumer = MagicMock()
        handler = PictionaryEventHandler('TEST001', mock_consumer)
        
        # 设置游戏状态：第3局，最多5局，但时间不够
        handler.current_round = 3
        handler.max_rounds = 5
        handler.step_start_time = timezone.now() - timedelta(seconds=540)  # 9分钟前开始
        handler.step_duration = 600  # 10分钟总时长
        handler.round_duration = 90  # 90秒每局
        
        # 虽然没有达到最大局数，但时间不够，应该结束
        handler.current_round += 1  # 模拟进入第4局
        has_more_rounds = (
            handler.current_round <= handler.max_rounds and 
            handler._has_enough_time_for_next_round()
        )
        self.assertFalse(has_more_rounds)

    def test_default_configuration(self):
        """测试默认配置"""
        # 创建没有配置的步骤
        step_no_config = EventStep.objects.create(
            template=self.template,
            name='默认配置测试',
            step_type='GAME_PICTIONARY',
            order=2,
            duration=300,  # 5分钟
            configuration={}  # 空配置
        )
        
        mock_consumer = MagicMock()
        handler = PictionaryEventHandler('TEST001', mock_consumer)
        
        # 模拟配置解析
        config = step_no_config.configuration or {}
        round_duration = config.get('round_duration', 60)  # 默认60秒
        max_rounds = config.get('max_rounds', 999)  # 默认999局
        
        self.assertEqual(round_duration, 60)
        self.assertEqual(max_rounds, 999)


if __name__ == '__main__':
    pytest.main([__file__])
