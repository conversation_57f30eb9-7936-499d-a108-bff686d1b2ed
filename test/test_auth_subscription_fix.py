"""
测试认证和订阅权限修复

验证以下修复：
1. JWT解码问题修复
2. 登录后订阅信息及时获取
3. 登出流程正确性
"""

import pytest
import json
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()


class TestAuthSubscriptionFix(TestCase):
    """测试认证和订阅权限修复"""

    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level='Max'
        )

    def test_jwt_token_contains_subscription_info(self):
        """测试JWT token包含订阅信息"""
        refresh = RefreshToken.for_user(self.user)
        access_token = refresh.access_token
        
        # 添加订阅信息到token
        access_token['subscription_level'] = self.user.subscription_level
        
        # 验证token包含正确的订阅信息
        self.assertEqual(access_token['subscription_level'], 'Max')
        self.assertEqual(access_token['username'], 'testuser')

    def test_login_api_returns_subscription_info(self):
        """测试登录API返回订阅信息"""
        response = self.client.post('/api/users/login/', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # 验证响应包含access token
        self.assertIn('access', data)
        
        # 解码token验证订阅信息
        from rest_framework_simplejwt.tokens import UntypedToken
        from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
        from jwt import decode
        from django.conf import settings
        
        try:
            token = UntypedToken(data['access'])
            decoded = decode(
                data['access'], 
                settings.SECRET_KEY, 
                algorithms=['HS256']
            )
            
            # 验证token包含订阅信息
            self.assertEqual(decoded['username'], 'testuser')
            # 注意：这里可能需要根据实际的token生成逻辑调整
            
        except (InvalidToken, TokenError) as e:
            self.fail(f"Token validation failed: {e}")

    def test_user_subscription_level(self):
        """测试用户的订阅等级"""
        self.assertEqual(self.user.subscription_level, 'Max')

        # 测试订阅等级更新
        self.user.subscription_level = 'Pro'
        self.user.save()

        self.user.refresh_from_db()
        self.assertEqual(self.user.subscription_level, 'Pro')

    def test_subscription_level_choices(self):
        """测试订阅等级选择"""
        valid_levels = ['Free', 'Pro', 'Max']

        for level in valid_levels:
            self.user.subscription_level = level
            self.user.save()
            self.user.refresh_from_db()
            self.assertEqual(self.user.subscription_level, level)

    @patch('core.views.logger')
    def test_login_error_handling(self, mock_logger):
        """测试登录错误处理"""
        # 测试错误的用户名密码
        response = self.client.post('/api/users/login/', {
            'username': 'wronguser',
            'password': 'wrongpass'
        })
        
        self.assertEqual(response.status_code, 401)
        data = response.json()
        self.assertIn('error', data)

    def test_token_refresh_preserves_subscription_info(self):
        """测试token刷新保持订阅信息"""
        # 首先登录获取token
        response = self.client.post('/api/users/login/', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # 使用refresh token获取新的access token
        if 'refresh' in data:
            refresh_response = self.client.post('/api/users/token/refresh/', {
                'refresh': data['refresh']
            })
            
            if refresh_response.status_code == 200:
                refresh_data = refresh_response.json()
                self.assertIn('access', refresh_data)
                
                # 验证新的access token也包含订阅信息
                # 这里需要根据实际的token刷新逻辑进行验证

    def test_logout_clears_session(self):
        """测试登出清除会话"""
        # 首先登录
        response = self.client.post('/api/users/login/', {
            'username': 'testuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        
        # 验证登录状态
        # 这里可以添加需要认证的API调用来验证登录状态
        
        # 执行登出
        # 注意：这里需要根据实际的登出API进行调整
        # logout_response = self.client.post('/api/users/logout/')
        
        # 验证登出后无法访问需要认证的资源
        # 这里可以添加相应的验证逻辑


class TestJWTTokenGeneration(TestCase):
    """测试JWT token生成"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='jwttest',
            password='testpass123'
        )
        self.user.subscription_level = 'Pro'
        self.user.save()

    def test_custom_token_claims(self):
        """测试自定义token声明"""
        from rest_framework_simplejwt.tokens import RefreshToken
        
        refresh = RefreshToken.for_user(self.user)
        access_token = refresh.access_token
        
        # 添加自定义声明
        access_token['username'] = self.user.username
        access_token['subscription_level'] = self.user.subscription_level
        
        # 验证自定义声明
        self.assertEqual(access_token['username'], 'jwttest')
        self.assertEqual(access_token['subscription_level'], 'Pro')
        
        # 验证token可以正确编码和解码
        token_string = str(access_token)
        self.assertIsInstance(token_string, str)
        self.assertTrue(len(token_string) > 0)


if __name__ == '__main__':
    pytest.main([__file__])
