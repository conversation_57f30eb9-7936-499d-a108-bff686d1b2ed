"""
订阅等级格式一致性测试

验证前后端订阅等级格式的一致性，确保：
1. User模型和SystemTemplate模型使用相同的订阅等级格式
2. API响应格式与前端期望一致
3. 权限验证逻辑正确工作
"""

import pytest
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
import json

from core.models import User, SystemTemplate, TemplateManager
from events.models import EventTemplate

User = get_user_model()


class SubscriptionConsistencyTest(TestCase):
    """订阅等级格式一致性测试"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
    
    def test_user_subscription_levels_format(self):
        """测试User模型订阅等级格式"""
        # 验证User模型的订阅等级常量
        self.assertEqual(User.SUBSCRIPTION_FREE, 'Free')
        self.assertEqual(User.SUBSCRIPTION_PRO, 'Pro')
        self.assertEqual(User.SUBSCRIPTION_MAX, 'Max')
        
        # 验证选择项格式
        expected_choices = [
            ('Free', 'Free'),
            ('Pro', 'Pro'),
            ('Max', 'Max'),
        ]
        self.assertEqual(User.SUBSCRIPTION_CHOICES, expected_choices)
    
    def test_system_template_subscription_levels_format(self):
        """测试SystemTemplate模型订阅等级格式"""
        # 验证SystemTemplate模型的订阅等级常量
        self.assertEqual(SystemTemplate.SUBSCRIPTION_FREE, 'Free')
        self.assertEqual(SystemTemplate.SUBSCRIPTION_PRO, 'Pro')
        self.assertEqual(SystemTemplate.SUBSCRIPTION_MAX, 'Max')
        
        # 验证选择项格式
        expected_choices = [
            ('Free', '免费版'),
            ('Pro', 'Pro版'),
            ('Max', 'Max版'),
        ]
        self.assertEqual(SystemTemplate.SUBSCRIPTION_CHOICES, expected_choices)
    
    def test_subscription_level_consistency(self):
        """测试User和SystemTemplate模型的订阅等级格式一致性"""
        # 验证两个模型使用相同的订阅等级值
        self.assertEqual(User.SUBSCRIPTION_FREE, SystemTemplate.SUBSCRIPTION_FREE)
        self.assertEqual(User.SUBSCRIPTION_PRO, SystemTemplate.SUBSCRIPTION_PRO)
        self.assertEqual(User.SUBSCRIPTION_MAX, SystemTemplate.SUBSCRIPTION_MAX)
    
    def test_system_template_access_permission(self):
        """测试系统模板访问权限验证"""
        # 创建不同订阅等级要求的系统模板
        free_template = SystemTemplate.objects.create(
            name='免费模板',
            description='免费用户可用',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={'steps': []}
        )
        
        pro_template = SystemTemplate.objects.create(
            name='Pro模板',
            description='Pro用户可用',
            required_subscription=SystemTemplate.SUBSCRIPTION_PRO,
            template_config={'steps': []}
        )
        
        max_template = SystemTemplate.objects.create(
            name='Max模板',
            description='Max用户可用',
            required_subscription=SystemTemplate.SUBSCRIPTION_MAX,
            template_config={'steps': []}
        )
        
        # 测试免费用户权限
        free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.assertTrue(free_template.is_accessible_by_user(free_user))
        self.assertFalse(pro_template.is_accessible_by_user(free_user))
        self.assertFalse(max_template.is_accessible_by_user(free_user))
        
        # 测试Pro用户权限
        pro_user = User.objects.create_user(
            username='prouser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        self.assertTrue(free_template.is_accessible_by_user(pro_user))
        self.assertTrue(pro_template.is_accessible_by_user(pro_user))
        self.assertFalse(max_template.is_accessible_by_user(pro_user))
        
        # 测试Max用户权限
        max_user = User.objects.create_user(
            username='maxuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_MAX
        )
        
        self.assertTrue(free_template.is_accessible_by_user(max_user))
        self.assertTrue(pro_template.is_accessible_by_user(max_user))
        self.assertTrue(max_template.is_accessible_by_user(max_user))


class SubscriptionAPIConsistencyTest(APITestCase):
    """订阅相关API格式一致性测试"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='apiuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        self.client = Client()
    
    def test_user_registration_response_format(self):
        """测试用户注册响应格式"""
        response = self.client.post('/api/users/register/', {
            'username': 'newuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 201)
        data = response.json()
        
        # 验证响应包含订阅等级字段
        self.assertIn('subscription_level', data)
        # 验证订阅等级格式正确
        self.assertEqual(data['subscription_level'], 'Free')
    
    def test_login_response_format(self):
        """测试登录响应格式"""
        response = self.client.post('/api/users/login/', {
            'username': 'apiuser',
            'password': 'testpass123'
        })

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # 验证响应包含JWT tokens
        self.assertIn('access', data)
        self.assertIn('refresh', data)

        # JWT token中应该包含订阅等级信息
        # 这里我们可以解码token来验证，但为了简化测试，我们验证token存在即可
        self.assertTrue(len(data['access']) > 0)
        self.assertTrue(len(data['refresh']) > 0)
    
    def test_subscription_management_response_format(self):
        """测试订阅管理API响应格式"""
        # 先登录获取token
        login_response = self.client.post('/api/users/login/', {
            'username': 'apiuser',
            'password': 'testpass123'
        })
        token = login_response.json()['access']
        
        # 测试获取订阅信息
        response = self.client.get('/api/subscription/', 
                                 HTTP_AUTHORIZATION=f'Bearer {token}')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # 验证响应格式
        self.assertIn('current_level', data)
        self.assertEqual(data['current_level'], 'Free')
        
        # 验证订阅信息结构
        self.assertIn('subscription_info', data)
        subscription_info = data['subscription_info']
        self.assertIn('Free', subscription_info)
        self.assertIn('Pro', subscription_info)
        self.assertIn('Max', subscription_info)


class TemplateManagerConsistencyTest(TestCase):
    """模板管理器一致性测试"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='templateuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        # 创建系统模板
        self.system_template = SystemTemplate.objects.create(
            name='系统模板',
            description='测试系统模板',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={'steps': []}
        )
        
        # 创建用户模板
        self.user_template = EventTemplate.objects.create(
            name='用户模板',
            description='测试用户模板',
            creator=self.user
        )
    
    def test_template_manager_get_accessible_templates(self):
        """测试模板管理器获取可访问模板"""
        accessible_templates = TemplateManager.get_available_templates_for_user(self.user)

        # 验证返回的模板包含系统模板和用户模板
        template_names = [t['name'] for t in accessible_templates]
        self.assertIn('系统模板', template_names)
        self.assertIn('用户模板', template_names)

        # 验证模板类型标识正确
        for template in accessible_templates:
            self.assertIn('type', template)
            self.assertIn(template['type'], ['system', 'user'])

    def test_template_id_format(self):
        """测试模板ID格式"""
        accessible_templates = TemplateManager.get_available_templates_for_user(self.user)

        for template in accessible_templates:
            template_id = template['id']

            if template['type'] == 'system':
                self.assertTrue(template_id.startswith('system_'))
            elif template['type'] == 'user':
                self.assertTrue(template_id.startswith('user_'))


if __name__ == '__main__':
    import django
    import os
    
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
    django.setup()
    
    import unittest
    unittest.main()
