"""
测试房间权限传递功能

验证：
1. 第一个加入OPEN状态房间的用户成为房主
2. 房主权限正确传递到前端
3. RoomParticipant模型正确记录用户角色
4. 序列化器正确返回参与者信息
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from core.models import Room, RoomParticipant, RoomState, UserState
from core.services.room_manager import room_manager
from core.serializers import RoomSerializer
from events.models import EventTemplate

User = get_user_model()


class TestRoomHostPermissions(TestCase):
    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.user1 = User.objects.create_user(
            username='user1',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        self.user2 = User.objects.create_user(
            username='user2', 
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='测试模板',
            description='用于测试的模板',
            creator=self.user1
        )

    def test_first_user_becomes_host_in_open_room(self):
        """测试第一个加入OPEN状态房间的用户成为房主"""
        # 创建OPEN状态的房间
        room = Room.objects.create(
            room_code='OPEN001',
            event_template=self.template,
            status=RoomState.OPEN,
            duration_hours=2
        )
        
        # 第一个用户加入房间
        success, message = room_manager.join_room_sync(room.room_code, self.user1)
        self.assertTrue(success, f"用户加入失败: {message}")
        
        # 刷新房间数据
        room.refresh_from_db()
        
        # 验证房间状态和房主
        self.assertEqual(room.host, self.user1)
        # 房间状态应该转换到WAITING或READY（取决于是否满足开始条件）
        self.assertIn(room.status, [RoomState.WAITING, RoomState.READY])
        
        # 验证RoomParticipant记录
        participant = RoomParticipant.objects.get(room=room, user=self.user1)
        self.assertEqual(participant.role, RoomParticipant.ROLE_HOST)
        self.assertEqual(participant.state, UserState.JOINED)
        self.assertIsNone(participant.left_at)

    def test_second_user_becomes_participant(self):
        """测试第二个用户加入房间成为参与者"""
        # 创建房间并让第一个用户加入成为房主
        room = Room.objects.create(
            room_code='OPEN002',
            event_template=self.template,
            status=RoomState.OPEN,
            duration_hours=2
        )
        
        # 第一个用户加入成为房主
        room_manager.join_room_sync(room.room_code, self.user1)
        
        # 第二个用户加入
        success, message = room_manager.join_room_sync(room.room_code, self.user2)
        self.assertTrue(success, f"第二个用户加入失败: {message}")
        
        # 验证第二个用户的角色
        participant2 = RoomParticipant.objects.get(room=room, user=self.user2)
        self.assertEqual(participant2.role, RoomParticipant.ROLE_PARTICIPANT)
        self.assertEqual(participant2.state, UserState.JOINED)

    def test_room_serializer_includes_participant_roles(self):
        """测试房间序列化器正确包含参与者角色信息"""
        # 创建房间并添加用户
        room = Room.objects.create(
            room_code='SERIAL001',
            event_template=self.template,
            status=RoomState.OPEN,
            duration_hours=2
        )
        
        # 添加房主和参与者
        room_manager.join_room_sync(room.room_code, self.user1)  # 房主
        room_manager.join_room_sync(room.room_code, self.user2)  # 参与者

        # 刷新房间数据
        room.refresh_from_db()

        # 序列化房间数据
        serializer = RoomSerializer(room)
        data = serializer.data
        
        # 验证序列化数据
        self.assertEqual(data['host'], self.user1.username)
        self.assertEqual(len(data['participants']), 2)
        
        # 验证参与者信息
        participants = {p['username']: p for p in data['participants']}
        
        self.assertIn(self.user1.username, participants)
        self.assertIn(self.user2.username, participants)
        
        # 验证角色信息
        self.assertEqual(participants[self.user1.username]['role'], 'host')
        self.assertEqual(participants[self.user2.username]['role'], 'participant')

    def test_host_permissions_in_ready_room(self):
        """测试READY状态房间中的房主权限"""
        # 创建READY状态的房间
        room = Room.objects.create(
            room_code='READY001',
            host=self.user1,
            event_template=self.template,
            status=RoomState.READY,
            duration_hours=2
        )
        
        # 手动添加房主参与者记录
        RoomParticipant.objects.create(
            room=room,
            user=self.user1,
            role=RoomParticipant.ROLE_HOST,
            state=UserState.JOINED
        )
        
        # 序列化房间数据
        serializer = RoomSerializer(room)
        data = serializer.data
        
        # 验证房主信息
        self.assertEqual(data['host'], self.user1.username)
        self.assertEqual(len(data['participants']), 1)
        self.assertEqual(data['participants'][0]['role'], 'host')
        self.assertEqual(data['participants'][0]['username'], self.user1.username)

    def test_room_state_transition_after_host_join(self):
        """测试房主加入后的房间状态转换"""
        # 创建OPEN状态的房间
        room = Room.objects.create(
            room_code='TRANS001',
            event_template=self.template,
            status=RoomState.OPEN,
            duration_hours=2
        )
        
        # 用户加入房间
        success, message = room_manager.join_room_sync(room.room_code, self.user1)
        self.assertTrue(success)
        
        # 刷新房间数据
        room.refresh_from_db()
        
        # 验证状态转换
        # OPEN -> WAITING (有人加入) -> READY (满足条件)
        self.assertIn(room.status, [RoomState.WAITING, RoomState.READY])
        
        # 验证房主设置
        self.assertEqual(room.host, self.user1)


if __name__ == '__main__':
    pytest.main([__file__])
