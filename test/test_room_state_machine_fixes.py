#!/usr/bin/env python3
"""
测试房间状态机修复

这个测试脚本验证以下修复：
1. IN_PROGRESS结束后回到READY状态，而不是ENDED状态
2. 房间过期检测机制：超过额定时间进入ENDED状态，15分钟后进入CLOSED状态
3. 房间无人状态处理：转换到OPEN状态而不是关闭
4. 预约房间房主分配：第一个加入的人成为房主
"""

import os
import sys
import django
from datetime import timedelta
from django.utils import timezone

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

from core.models import Room, RoomState, User, RoomParticipant
from core.services.room_manager import room_manager
from core.utils import advance_to_next_step_sync
from events.models import EventTemplate, EventStep
from django.db import transaction


def setup_test_data():
    """设置测试数据"""
    print("🔧 设置测试数据...")
    
    # 创建测试用户
    user1, _ = User.objects.get_or_create(
        username='test_user1',
        defaults={'email': '<EMAIL>'}
    )
    user2, _ = User.objects.get_or_create(
        username='test_user2', 
        defaults={'email': '<EMAIL>'}
    )
    
    # 创建测试模板
    template, _ = EventTemplate.objects.get_or_create(
        name='测试模板',
        defaults={
            'description': '用于测试状态机修复',
            'created_by': user1
        }
    )
    
    # 清理旧的环节
    EventStep.objects.filter(template=template).delete()
    
    # 添加测试环节
    EventStep.objects.create(
        template=template,
        name='测试聊天',
        step_type='CHAT',
        duration=60,
        order=1
    )
    
    return user1, user2, template


def test_state_machine_flow():
    """测试1: 状态机流程 - IN_PROGRESS结束后回到READY"""
    print("\n📋 测试1: 状态机流程")

    user1, user2, template = setup_test_data()

    # 清理旧房间
    Room.objects.filter(room_code='TEST_SM').delete()

    # 创建房间
    room = Room.objects.create(
        room_code='TEST_SM',
        host=user1,
        event_template=template,
        status=RoomState.READY,
        current_step_order=0
    )
    
    print(f"   创建房间: {room.room_code}, 初始状态: {room.status}")
    
    # 推进到第一个环节
    next_step = advance_to_next_step_sync(room)
    room.refresh_from_db()
    print(f"   推进到环节1: 状态={room.status}, 环节={next_step.name if next_step else None}")

    assert room.status == RoomState.IN_PROGRESS, f"期望IN_PROGRESS，实际{room.status}"

    # 再次推进（应该回到READY状态）
    next_step = advance_to_next_step_sync(room)
    room.refresh_from_db()
    print(f"   环节结束: 状态={room.status}, 下一环节={next_step}")
    
    assert room.status == RoomState.READY, f"期望READY，实际{room.status}"
    assert next_step is None, "期望没有更多环节"
    
    print("   ✅ 状态机流程测试通过")
    
    # 清理
    room.delete()


def test_room_expiry():
    """测试2: 房间过期检测机制"""
    print("\n⏰ 测试2: 房间过期检测")

    user1, user2, template = setup_test_data()

    # 清理旧房间
    Room.objects.filter(room_code='TEST_EXP').delete()

    # 创建过期房间
    past_time = timezone.now() - timedelta(hours=1)
    room = Room.objects.create(
        room_code='TEST_EXP',
        host=user1,
        event_template=template,
        status=RoomState.IN_PROGRESS,
        expires_at=past_time,
        current_step_order=1
    )
    
    print(f"   创建过期房间: {room.room_code}, 状态: {room.status}")
    print(f"   过期时间: {room.expires_at}")
    
    # 模拟过期检测
    from core.services.room_lifecycle import RoomLifecycleManager
    import asyncio
    
    async def test_expiry():
        manager = RoomLifecycleManager()
        count = await manager._end_expired_rooms()
        return count
    
    count = asyncio.run(test_expiry())
    room.refresh_from_db()
    
    print(f"   过期检测结果: 处理了{count}个房间, 新状态: {room.status}")
    assert room.status == RoomState.ENDED, f"期望ENDED，实际{room.status}"
    
    print("   ✅ 房间过期检测测试通过")
    
    # 清理
    room.delete()


def test_empty_room_handling():
    """测试3: 空房间处理"""
    print("\n🏠 测试3: 空房间处理")

    user1, user2, template = setup_test_data()

    # 清理旧房间
    Room.objects.filter(room_code='TEST_EMPTY').delete()

    # 创建有参与者的房间
    room = Room.objects.create(
        room_code='TEST_EMPTY',
        host=user1,
        event_template=template,
        status=RoomState.READY
    )

    # 手动设置过期的最后活动时间
    room.last_activity_at = timezone.now() - timedelta(minutes=6)  # 超过5分钟阈值
    room.save()
    
    # 添加参与者
    participant = RoomParticipant.objects.create(
        room=room,
        user=user1,
        role=RoomParticipant.ROLE_HOST,
        is_active=True
    )
    
    print(f"   创建房间: {room.room_code}, 状态: {room.status}")
    
    # 模拟房主离开
    participant.is_active = False
    participant.left_at = timezone.now()
    participant.save()

    print(f"   房主离开后，活跃参与者数量: {RoomParticipant.objects.filter(room=room, is_active=True).count()}")
    print(f"   房间最后活动时间: {room.last_activity_at}")
    print(f"   当前时间: {timezone.now()}")
    print(f"   时间差: {timezone.now() - room.last_activity_at}")

    # 模拟空房间检测
    from core.services.room_lifecycle import RoomLifecycleManager
    import asyncio

    async def test_empty():
        manager = RoomLifecycleManager()
        # 先检查能找到哪些空房间
        empty_rooms = await manager._get_empty_rooms_to_open()
        print(f"   找到的空房间: {empty_rooms}")
        count = await manager._handle_empty_rooms()
        return count

    count = asyncio.run(test_empty())
    room.refresh_from_db()
    
    print(f"   空房间处理结果: 处理了{count}个房间, 新状态: {room.status}")
    assert room.status == RoomState.OPEN, f"期望OPEN，实际{room.status}"
    
    print("   ✅ 空房间处理测试通过")
    
    # 清理
    room.delete()


def test_host_assignment():
    """测试4: 预约房间房主分配 - 新状态机流程"""
    print("\n👑 测试4: 预约房间房主分配")

    user1, user2, template = setup_test_data()

    # 清理旧房间
    Room.objects.filter(room_code='TEST_HOST').delete()

    # 创建OPEN状态的房间（模拟预约房间被激活）
    room = Room.objects.create(
        room_code='TEST_HOST',
        host=user1,  # 原始创建者
        event_template=template,
        status=RoomState.OPEN
    )

    print(f"   创建OPEN房间: {room.room_code}, 原房主: {room.host.username}")

    # 第一个用户加入（应该成为新房主，状态流转：OPEN -> WAITING -> READY）
    success, message = room_manager.join_room_sync('TEST_HOST', user2)
    room.refresh_from_db()

    print(f"   用户{user2.username}加入结果: {success}, {message}")
    print(f"   房间新状态: {room.status}, 新房主: {room.host.username}")

    assert success, f"加入失败: {message}"
    assert room.host == user2, f"期望房主是{user2.username}，实际是{room.host.username}"
    assert room.status == RoomState.READY, f"期望READY，实际{room.status}"

    # 检查参与者记录
    participant = RoomParticipant.objects.get(room=room, user=user2)
    assert participant.role == RoomParticipant.ROLE_HOST, "期望用户是房主角色"

    print("   ✅ 预约房间房主分配测试通过（OPEN->WAITING->READY流程）")

    # 清理
    room.delete()


def test_add_step_in_ready_state():
    """测试5: 在READY状态下添加环节"""
    print("\n➕ 测试5: READY状态下添加环节")

    user1, user2, template = setup_test_data()

    # 清理旧房间
    Room.objects.filter(room_code='TEST_ADD').delete()

    # 创建READY状态的房间
    room = Room.objects.create(
        room_code='TEST_ADD',
        host=user1,
        event_template=template,
        status=RoomState.READY,
        current_step_order=1
    )
    
    print(f"   创建READY房间: {room.room_code}")
    
    # 尝试添加环节
    from django.test import RequestFactory
    from django.contrib.auth.models import AnonymousUser
    from core.views import AddStepToRoomView
    
    factory = RequestFactory()
    request = factory.post(f'/api/rooms/{room.room_code}/add-step/', {
        'step_type': 'CHAT',
        'name': '新聊天环节',
        'duration': 120
    }, content_type='application/json')
    request.user = user1
    
    view = AddStepToRoomView()
    response = view.post(request, room_code=room.room_code)
    
    print(f"   添加环节结果: 状态码={response.status_code}")
    
    if response.status_code == 201:
        print("   ✅ READY状态下添加环节测试通过")
    else:
        print(f"   ❌ 添加环节失败: {response.data}")
    
    # 清理
    room.delete()


def main():
    """运行所有测试"""
    print("🧪 开始房间状态机修复测试")
    print("=" * 50)
    
    try:
        test_state_machine_flow()
        test_room_expiry()
        test_empty_room_handling()
        test_host_assignment()
        test_add_step_in_ready_state()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！房间状态机修复成功")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
