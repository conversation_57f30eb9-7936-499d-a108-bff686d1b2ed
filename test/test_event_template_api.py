"""
测试事件模板API功能

验证：
1. 用户可以创建事件模板
2. 用户可以获取自己创建的事件模板列表
3. 环节编辑器API正确返回用户模板
4. 创建模板后能立即在列表中看到
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from events.models import EventTemplate, EventStep

User = get_user_model()


class TestEventTemplateAPI(TestCase):
    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.user1 = User.objects.create_user(
            username='user1',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.user2 = User.objects.create_user(
            username='user2',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        # 创建API客户端
        self.client = APIClient()

    def test_create_event_template(self):
        """测试创建事件模板"""
        self.client.force_authenticate(user=self.user1)
        
        data = {
            'name': '测试模板',
            'description': '这是一个测试模板'
        }
        
        response = self.client.post('/api/events/templates/', data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证模板创建成功
        template = EventTemplate.objects.get(name='测试模板')
        self.assertEqual(template.creator, self.user1)
        self.assertEqual(template.description, '这是一个测试模板')

    def test_get_user_event_templates(self):
        """测试获取用户事件模板列表"""
        # 创建测试模板
        template1 = EventTemplate.objects.create(
            name='用户1模板1',
            description='用户1的第一个模板',
            creator=self.user1
        )
        
        template2 = EventTemplate.objects.create(
            name='用户1模板2',
            description='用户1的第二个模板',
            creator=self.user1
        )
        
        # 创建其他用户的模板（不应该出现在结果中）
        EventTemplate.objects.create(
            name='用户2模板',
            description='用户2的模板',
            creator=self.user2
        )
        
        # 用户1获取模板列表
        self.client.force_authenticate(user=self.user1)
        response = self.client.get('/api/events/templates/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        templates = response.json()
        
        # 应该只返回用户1的模板
        self.assertEqual(len(templates), 2)
        template_names = [t['name'] for t in templates]
        self.assertIn('用户1模板1', template_names)
        self.assertIn('用户1模板2', template_names)
        self.assertNotIn('用户2模板', template_names)

    def test_template_isolation_between_users(self):
        """测试用户之间的模板隔离"""
        # 用户1创建模板
        self.client.force_authenticate(user=self.user1)
        self.client.post('/api/events/templates/', {
            'name': '用户1的私有模板',
            'description': '只有用户1能看到'
        })
        
        # 用户2获取模板列表
        self.client.force_authenticate(user=self.user2)
        response = self.client.get('/api/events/templates/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        templates = response.json()
        
        # 用户2不应该看到用户1的模板
        template_names = [t['name'] for t in templates]
        self.assertNotIn('用户1的私有模板', template_names)

    def test_create_template_then_get_list(self):
        """测试创建模板后立即获取列表（模拟环节编辑器的使用场景）"""
        self.client.force_authenticate(user=self.user1)
        
        # 1. 获取初始模板列表（应该为空）
        response = self.client.get('/api/events/templates/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        initial_templates = response.json()
        self.assertEqual(len(initial_templates), 0)
        
        # 2. 创建新模板
        create_response = self.client.post('/api/events/templates/', {
            'name': '新创建的模板',
            'description': '刚刚创建的模板'
        })
        self.assertEqual(create_response.status_code, status.HTTP_201_CREATED)
        
        # 3. 再次获取模板列表（应该包含新创建的模板）
        response = self.client.get('/api/events/templates/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        updated_templates = response.json()
        
        # 验证新模板出现在列表中
        self.assertEqual(len(updated_templates), 1)
        self.assertEqual(updated_templates[0]['name'], '新创建的模板')
        self.assertEqual(updated_templates[0]['description'], '刚刚创建的模板')

    def test_template_with_steps(self):
        """测试包含步骤的模板"""
        self.client.force_authenticate(user=self.user1)
        
        # 创建模板
        template_response = self.client.post('/api/events/templates/', {
            'name': '带步骤的模板',
            'description': '包含多个步骤的模板'
        })
        template_id = template_response.json()['id']
        
        # 添加步骤
        step_response = self.client.post(f'/api/events/templates/{template_id}/add-step/', {
            'name': '第一个步骤',
            'step_type': 'FREE_CHAT',
            'duration': 600  # 10分钟
        })
        self.assertEqual(step_response.status_code, status.HTTP_201_CREATED)
        
        # 获取模板详情
        detail_response = self.client.get(f'/api/events/templates/{template_id}/')
        self.assertEqual(detail_response.status_code, status.HTTP_200_OK)
        
        template_detail = detail_response.json()
        self.assertEqual(len(template_detail['steps']), 1)
        self.assertEqual(template_detail['steps'][0]['name'], '第一个步骤')

    def test_unauthorized_access(self):
        """测试未认证用户的访问"""
        # 不进行认证
        response = self.client.get('/api/events/templates/')
        # 可能返回401或403，都表示未授权
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

        response = self.client.post('/api/events/templates/', {
            'name': '测试模板',
            'description': '测试描述'
        })
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])


if __name__ == '__main__':
    pytest.main([__file__])
