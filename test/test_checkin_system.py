"""
测试签到系统功能

验证签到模型、API和业务逻辑的正确性
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from django.utils import timezone
from datetime import date, timedelta

from core.models import CheckIn

User = get_user_model()


class CheckInModelTest(TestCase):
    """测试签到模型的基本功能"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
    
    def test_check_in_creation(self):
        """测试签到记录创建"""
        today = timezone.now().date()
        
        check_in = CheckIn.objects.create(
            user=self.user,
            check_in_date=today,
            check_in_type=CheckIn.CHECK_IN_TYPE_DAILY,
            consecutive_days=1,
            reward_points=1
        )
        
        self.assertEqual(check_in.user, self.user)
        self.assertEqual(check_in.check_in_date, today)
        self.assertEqual(check_in.check_in_type, CheckIn.CHECK_IN_TYPE_DAILY)
        self.assertEqual(check_in.consecutive_days, 1)
        self.assertEqual(check_in.reward_points, 1)
    
    def test_unique_daily_check_in(self):
        """测试每日签到的唯一性约束"""
        today = timezone.now().date()
        
        # 创建第一个签到记录
        CheckIn.objects.create(
            user=self.user,
            check_in_date=today,
            check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
        )
        
        # 尝试创建重复的签到记录应该失败
        with self.assertRaises(Exception):
            CheckIn.objects.create(
                user=self.user,
                check_in_date=today,
                check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
            )
    
    def test_has_checked_in_today(self):
        """测试今日签到检查"""
        today = timezone.now().date()
        
        # 初始状态：未签到
        self.assertFalse(CheckIn.has_checked_in_today(self.user))
        
        # 创建签到记录
        CheckIn.objects.create(
            user=self.user,
            check_in_date=today,
            check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
        )
        
        # 检查已签到
        self.assertTrue(CheckIn.has_checked_in_today(self.user))
    
    def test_consecutive_days_calculation(self):
        """测试连续签到天数计算"""
        today = timezone.now().date()
        
        # 创建连续3天的签到记录
        for i in range(3):
            check_date = today - timedelta(days=i)
            CheckIn.objects.create(
                user=self.user,
                check_in_date=check_date,
                check_in_type=CheckIn.CHECK_IN_TYPE_DAILY,
                consecutive_days=3-i
            )
        
        # 验证连续签到天数
        consecutive_days = CheckIn.get_user_consecutive_days(self.user)
        self.assertEqual(consecutive_days, 3)
    
    def test_consecutive_days_with_gap(self):
        """测试有间隔的签到天数计算"""
        today = timezone.now().date()
        
        # 今天签到
        CheckIn.objects.create(
            user=self.user,
            check_in_date=today,
            check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
        )
        
        # 3天前签到（中间有间隔）
        CheckIn.objects.create(
            user=self.user,
            check_in_date=today - timedelta(days=3),
            check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
        )
        
        # 连续天数应该只计算今天
        consecutive_days = CheckIn.get_user_consecutive_days(self.user)
        self.assertEqual(consecutive_days, 1)
    
    def test_total_check_ins(self):
        """测试总签到天数统计"""
        today = timezone.now().date()
        
        # 创建5天的签到记录（不连续）
        check_dates = [
            today,
            today - timedelta(days=1),
            today - timedelta(days=3),
            today - timedelta(days=7),
            today - timedelta(days=10)
        ]
        
        for check_date in check_dates:
            CheckIn.objects.create(
                user=self.user,
                check_in_date=check_date,
                check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
            )
        
        total_check_ins = CheckIn.get_user_total_check_ins(self.user)
        self.assertEqual(total_check_ins, 5)


class CheckInAPITest(APITestCase):
    """测试签到API端点"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_get_check_in_status(self):
        """测试获取签到状态"""
        response = self.client.get('/api/checkin/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertIn('has_checked_in_today', data)
        self.assertIn('consecutive_days', data)
        self.assertIn('total_check_ins', data)
        self.assertIn('month_check_in_dates', data)
        self.assertIn('today', data)
        
        # 初始状态验证
        self.assertFalse(data['has_checked_in_today'])
        self.assertEqual(data['consecutive_days'], 0)
        self.assertEqual(data['total_check_ins'], 0)
        self.assertEqual(len(data['month_check_in_dates']), 0)
    
    def test_perform_check_in(self):
        """测试执行签到"""
        response = self.client.post('/api/checkin/')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        data = response.json()
        self.assertIn('message', data)
        self.assertIn('check_in_date', data)
        self.assertIn('consecutive_days', data)
        self.assertIn('reward_points', data)
        self.assertIn('total_check_ins', data)
        
        # 验证签到结果
        self.assertEqual(data['consecutive_days'], 1)
        self.assertEqual(data['reward_points'], 1)
        self.assertEqual(data['total_check_ins'], 1)
        
        # 验证数据库记录
        today = timezone.now().date()
        check_in = CheckIn.objects.get(user=self.user, check_in_date=today)
        self.assertEqual(check_in.consecutive_days, 1)
        self.assertEqual(check_in.reward_points, 1)
    
    def test_duplicate_check_in_prevention(self):
        """测试防止重复签到"""
        # 第一次签到
        response = self.client.post('/api/checkin/')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 第二次签到应该失败
        response = self.client.post('/api/checkin/')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        data = response.json()
        self.assertIn('error', data)
        self.assertEqual(data['error'], '今天已经签到过了')
    
    def test_check_in_status_after_check_in(self):
        """测试签到后的状态查询"""
        # 执行签到
        self.client.post('/api/checkin/')
        
        # 查询状态
        response = self.client.get('/api/checkin/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        data = response.json()
        self.assertTrue(data['has_checked_in_today'])
        self.assertEqual(data['consecutive_days'], 1)
        self.assertEqual(data['total_check_ins'], 1)
        self.assertEqual(len(data['month_check_in_dates']), 1)
    
    def test_unauthenticated_access(self):
        """测试未认证访问"""
        self.client.force_authenticate(user=None)

        # GET请求
        response = self.client.get('/api/checkin/')
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

        # POST请求
        response = self.client.post('/api/checkin/')
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])


class CheckInBusinessLogicTest(TestCase):
    """测试签到业务逻辑"""
    
    def setUp(self):
        self.user1 = User.objects.create_user(
            username='user1',
            password='testpass123'
        )
        self.user2 = User.objects.create_user(
            username='user2',
            password='testpass123'
        )
    
    def test_multiple_users_independence(self):
        """测试多用户签到独立性"""
        today = timezone.now().date()
        
        # 用户1签到
        CheckIn.objects.create(
            user=self.user1,
            check_in_date=today,
            check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
        )
        
        # 验证用户1已签到，用户2未签到
        self.assertTrue(CheckIn.has_checked_in_today(self.user1))
        self.assertFalse(CheckIn.has_checked_in_today(self.user2))
        
        # 验证统计独立性
        self.assertEqual(CheckIn.get_user_total_check_ins(self.user1), 1)
        self.assertEqual(CheckIn.get_user_total_check_ins(self.user2), 0)
    
    def test_month_boundary_check_ins(self):
        """测试跨月签到记录"""
        # 创建跨月的签到记录
        today = timezone.now().date()
        last_month = today.replace(day=1) - timedelta(days=1)
        
        # 上个月签到
        CheckIn.objects.create(
            user=self.user1,
            check_in_date=last_month,
            check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
        )
        
        # 本月签到
        CheckIn.objects.create(
            user=self.user1,
            check_in_date=today,
            check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
        )
        
        # 总签到天数应该包含两个月
        total_check_ins = CheckIn.get_user_total_check_ins(self.user1)
        self.assertEqual(total_check_ins, 2)
        
        # 连续签到天数应该只计算本月（因为有间隔）
        consecutive_days = CheckIn.get_user_consecutive_days(self.user1)
        self.assertEqual(consecutive_days, 1)
