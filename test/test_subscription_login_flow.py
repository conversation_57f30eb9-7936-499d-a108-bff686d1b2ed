"""
订阅状态获取和渲染流程测试

验证以下要求：
1. 登录时前端能正确获取用户订阅状态
2. 前端能正确渲染订阅状态而不是使用默认值
3. 订阅系统不会影响登录或登出流程
4. JWT Token包含订阅等级信息
"""

import pytest
import json
import jwt
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from django.conf import settings

from core.models import User
from core.serializers import CustomTokenObtainPairSerializer

User = get_user_model()


class SubscriptionLoginFlowTest(TestCase):
    """订阅状态获取和渲染流程测试"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建不同订阅等级的用户
        self.free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.pro_user = User.objects.create_user(
            username='prouser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        self.max_user = User.objects.create_user(
            username='maxuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_MAX
        )
        
        self.client = Client()
    
    def test_login_includes_subscription_in_jwt(self):
        """测试登录时JWT Token包含订阅等级信息"""
        
        # 测试免费用户登录
        response = self.client.post('/api/users/login/', {
            'username': 'freeuser',
            'password': 'testpass123'
        })
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # 验证响应包含JWT tokens
        self.assertIn('access', data)
        self.assertIn('refresh', data)
        
        # 解码JWT token验证包含订阅等级
        access_token = data['access']
        decoded_token = jwt.decode(
            access_token, 
            settings.SECRET_KEY, 
            algorithms=['HS256']
        )
        
        # 验证token包含用户信息和订阅等级
        self.assertIn('username', decoded_token)
        self.assertIn('subscription_level', decoded_token)
        self.assertEqual(decoded_token['username'], 'freeuser')
        self.assertEqual(decoded_token['subscription_level'], 'Free')
        
        # 测试Pro用户登录
        response = self.client.post('/api/users/login/', {
            'username': 'prouser',
            'password': 'testpass123'
        })
        
        access_token = response.json()['access']
        decoded_token = jwt.decode(
            access_token, 
            settings.SECRET_KEY, 
            algorithms=['HS256']
        )
        
        self.assertEqual(decoded_token['subscription_level'], 'Pro')
        
        # 测试Max用户登录
        response = self.client.post('/api/users/login/', {
            'username': 'maxuser',
            'password': 'testpass123'
        })
        
        access_token = response.json()['access']
        decoded_token = jwt.decode(
            access_token, 
            settings.SECRET_KEY, 
            algorithms=['HS256']
        )
        
        self.assertEqual(decoded_token['subscription_level'], 'Max')
    
    def test_subscription_api_returns_correct_info(self):
        """测试订阅API返回正确的用户订阅信息"""
        
        # 测试免费用户
        login_response = self.client.post('/api/users/login/', {
            'username': 'freeuser',
            'password': 'testpass123'
        })
        token = login_response.json()['access']
        
        subscription_response = self.client.get('/api/subscription/', 
                                              HTTP_AUTHORIZATION=f'Bearer {token}')
        
        self.assertEqual(subscription_response.status_code, 200)
        data = subscription_response.json()
        
        # 验证返回正确的订阅信息
        self.assertEqual(data['current_level'], 'Free')
        self.assertEqual(data['username'], 'freeuser')
        self.assertIn('subscription_info', data)
        
        # 验证订阅限制信息
        subscription_info = data['subscription_info']
        self.assertEqual(subscription_info['Free']['max_participants'], 10)
        self.assertEqual(subscription_info['Free']['duration_hours'], 2)
        
        # 测试Pro用户
        login_response = self.client.post('/api/users/login/', {
            'username': 'prouser',
            'password': 'testpass123'
        })
        token = login_response.json()['access']
        
        subscription_response = self.client.get('/api/subscription/', 
                                              HTTP_AUTHORIZATION=f'Bearer {token}')
        data = subscription_response.json()
        
        self.assertEqual(data['current_level'], 'Pro')
        self.assertEqual(data['username'], 'prouser')
    
    def test_subscription_does_not_interfere_with_login_flow(self):
        """测试订阅系统不会干扰登录流程"""
        
        # 测试正常登录流程
        response = self.client.post('/api/users/login/', {
            'username': 'freeuser',
            'password': 'testpass123'
        })
        
        # 登录应该成功
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # 应该包含必要的认证信息
        self.assertIn('access', data)
        self.assertIn('refresh', data)
        
        # 测试错误的登录信息
        response = self.client.post('/api/users/login/', {
            'username': 'wronguser',
            'password': 'wrongpass'
        })
        
        # 应该返回认证错误，而不是订阅相关错误
        self.assertEqual(response.status_code, 401)
    
    def test_subscription_does_not_interfere_with_logout_flow(self):
        """测试订阅系统不会干扰登出流程"""
        
        # 先登录
        login_response = self.client.post('/api/users/login/', {
            'username': 'freeuser',
            'password': 'testpass123'
        })
        token = login_response.json()['access']
        
        # 验证token有效
        auth_response = self.client.get('/api/health-check/',
                                      HTTP_AUTHORIZATION=f'Bearer {token}')
        self.assertEqual(auth_response.status_code, 200)

        # 登出（前端通过删除token实现，后端无需特殊处理）
        # 验证删除token后无法访问需要认证的端点
        unauth_response = self.client.get('/api/health-check/')
        # 401或403都表示未认证/无权限，都是正确的
        self.assertIn(unauth_response.status_code, [401, 403])
    
    def test_subscription_update_provides_new_token(self):
        """测试订阅更新时提供新的JWT Token"""
        
        # 登录免费用户
        login_response = self.client.post('/api/users/login/', {
            'username': 'freeuser',
            'password': 'testpass123'
        })
        old_token = login_response.json()['access']
        
        # 解码旧token验证订阅等级
        old_decoded = jwt.decode(
            old_token, 
            settings.SECRET_KEY, 
            algorithms=['HS256']
        )
        self.assertEqual(old_decoded['subscription_level'], 'Free')
        
        # 更新订阅等级（调试模式）
        update_response = self.client.post('/api/subscription/', {
            'target_level': 'Pro',
            'is_debug': True
        }, HTTP_AUTHORIZATION=f'Bearer {old_token}',
           HTTP_X_DEBUG_MODE='true')
        
        self.assertEqual(update_response.status_code, 200)
        update_data = update_response.json()
        
        # 验证返回新的token
        self.assertIn('access_token', update_data)
        self.assertIn('new_level', update_data)
        self.assertEqual(update_data['new_level'], 'Pro')

        # 验证用户订阅等级已在数据库中更新
        self.free_user.refresh_from_db()
        self.assertEqual(self.free_user.subscription_level, 'Pro')

        # 验证新token可以正常使用
        new_token = update_data['access_token']
        test_response = self.client.get('/api/subscription/',
                                      HTTP_AUTHORIZATION=f'Bearer {new_token}')
        self.assertEqual(test_response.status_code, 200)
        test_data = test_response.json()
        self.assertEqual(test_data['current_level'], 'Pro')
    
    def test_subscription_context_integration(self):
        """测试订阅上下文集成（模拟前端行为）"""
        
        # 模拟前端登录流程
        login_response = self.client.post('/api/users/login/', {
            'username': 'prouser',
            'password': 'testpass123'
        })
        
        self.assertEqual(login_response.status_code, 200)
        token = login_response.json()['access']
        
        # 模拟前端获取订阅信息（SubscriptionContext的行为）
        subscription_response = self.client.get('/api/subscription/', 
                                              HTTP_AUTHORIZATION=f'Bearer {token}')
        
        self.assertEqual(subscription_response.status_code, 200)
        subscription_data = subscription_response.json()
        
        # 验证前端能获取到正确的订阅信息用于渲染
        self.assertEqual(subscription_data['current_level'], 'Pro')
        self.assertIn('subscription_info', subscription_data)
        
        # 验证订阅信息包含所有等级的限制信息
        subscription_info = subscription_data['subscription_info']
        for level in ['Free', 'Pro', 'Max']:
            self.assertIn(level, subscription_info)
            self.assertIn('max_participants', subscription_info[level])
            self.assertIn('duration_hours', subscription_info[level])
    
    def test_subscription_level_affects_room_creation(self):
        """测试订阅等级影响房间创建限制"""
        
        # 创建一个简单的用户模板用于测试
        from events.models import EventTemplate
        template = EventTemplate.objects.create(
            name='测试模板',
            description='用于测试的模板',
            creator=self.free_user
        )
        
        # 免费用户登录
        login_response = self.client.post('/api/users/login/', {
            'username': 'freeuser',
            'password': 'testpass123'
        })
        token = login_response.json()['access']
        
        # 创建房间
        room_response = self.client.post('/api/rooms/', {
            'template_id': f'user_{template.id}'
        }, HTTP_AUTHORIZATION=f'Bearer {token}')
        
        self.assertEqual(room_response.status_code, 201)
        room_data = room_response.json()
        
        # 验证房间限制根据订阅等级设置
        # 注意：这里我们无法直接从API响应中看到限制，
        # 但可以通过数据库验证
        from core.models import Room
        room = Room.objects.get(room_code=room_data['room_code'])
        self.assertEqual(room.max_participants, 10)  # 免费用户限制
        self.assertEqual(room.duration_hours, 2)     # 免费用户限制


class CustomTokenSerializerTest(TestCase):
    """自定义Token序列化器测试"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
    
    def test_custom_token_includes_subscription_level(self):
        """测试自定义Token序列化器包含订阅等级"""
        
        serializer = CustomTokenObtainPairSerializer()
        token = serializer.get_token(self.user)
        
        # 验证token包含用户名和订阅等级
        self.assertEqual(token['username'], 'testuser')
        self.assertEqual(token['subscription_level'], 'Pro')
        
        # 验证access token也包含这些信息
        access_token = token.access_token
        decoded = jwt.decode(
            str(access_token), 
            settings.SECRET_KEY, 
            algorithms=['HS256']
        )
        
        self.assertEqual(decoded['username'], 'testuser')
        self.assertEqual(decoded['subscription_level'], 'Pro')


if __name__ == '__main__':
    import django
    import os
    
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
    django.setup()
    
    import unittest
    unittest.main()
