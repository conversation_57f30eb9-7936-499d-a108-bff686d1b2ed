"""
系统模板核心要求验证测试

验证系统模板的三个核心要求：
1. 每一个用户都能调用系统模板
2. 单独分出一个数据表储存系统模板，不与针对每个用户的动态模板库混合
3. 系统模板不能在个人模板库中进行修改
"""

import pytest
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
import json

from core.models import User, SystemTemplate, TemplateManager
from events.models import EventTemplate, EventStep

User = get_user_model()


class SystemTemplateRequirementsTest(TestCase):
    """系统模板核心要求验证测试"""
    
    def setUp(self):
        """设置测试数据"""
        # 创建不同订阅等级的用户
        self.free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.pro_user = User.objects.create_user(
            username='prouser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        self.max_user = User.objects.create_user(
            username='maxuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_MAX
        )
        
        # 创建系统模板
        self.free_system_template = SystemTemplate.objects.create(
            name='免费系统模板',
            description='所有用户都可以使用的免费模板',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {
                        'id': 'step_1',
                        'name': '自由讨论',
                        'step_type': 'FREE_CHAT',
                        'order': 1,
                        'duration': 300,
                        'configuration': {}
                    }
                ]
            }
        )
        
        self.pro_system_template = SystemTemplate.objects.create(
            name='Pro系统模板',
            description='需要Pro版本的系统模板',
            required_subscription=SystemTemplate.SUBSCRIPTION_PRO,
            template_config={
                'steps': [
                    {
                        'id': 'step_1',
                        'name': '高级讨论',
                        'step_type': 'FREE_CHAT',
                        'order': 1,
                        'duration': 600,
                        'configuration': {}
                    }
                ]
            }
        )
        
        # 创建用户模板
        self.user_template = EventTemplate.objects.create(
            name='用户模板',
            description='用户创建的模板',
            creator=self.free_user
        )
    
    def test_requirement_1_all_users_can_access_system_templates(self):
        """要求1：每一个用户都能调用系统模板（根据订阅等级）"""
        
        # 测试免费用户
        free_templates = TemplateManager.get_available_templates_for_user(self.free_user)
        free_system_templates = [t for t in free_templates if t['type'] == 'system']
        
        # 免费用户应该能看到免费系统模板
        free_template_names = [t['name'] for t in free_system_templates]
        self.assertIn('免费系统模板', free_template_names)
        self.assertNotIn('Pro系统模板', free_template_names)
        
        # 测试Pro用户
        pro_templates = TemplateManager.get_available_templates_for_user(self.pro_user)
        pro_system_templates = [t for t in pro_templates if t['type'] == 'system']
        
        # Pro用户应该能看到所有系统模板
        pro_template_names = [t['name'] for t in pro_system_templates]
        self.assertIn('免费系统模板', pro_template_names)
        self.assertIn('Pro系统模板', pro_template_names)
        
        # 测试Max用户
        max_templates = TemplateManager.get_available_templates_for_user(self.max_user)
        max_system_templates = [t for t in max_templates if t['type'] == 'system']
        
        # Max用户应该能看到所有系统模板
        max_template_names = [t['name'] for t in max_system_templates]
        self.assertIn('免费系统模板', max_template_names)
        self.assertIn('Pro系统模板', max_template_names)
    
    def test_requirement_2_separate_data_tables(self):
        """要求2：单独分出一个数据表储存系统模板，不与用户模板混合"""
        
        # 验证SystemTemplate和EventTemplate是不同的数据表
        self.assertNotEqual(SystemTemplate._meta.db_table, EventTemplate._meta.db_table)
        
        # 验证系统模板存储在SystemTemplate表中
        system_templates_count = SystemTemplate.objects.count()
        self.assertGreaterEqual(system_templates_count, 2)  # 至少有我们创建的2个
        
        # 验证用户模板存储在EventTemplate表中
        user_templates_count = EventTemplate.objects.count()
        self.assertGreaterEqual(user_templates_count, 1)  # 至少有我们创建的1个
        
        # 验证系统模板不会出现在EventTemplate表中
        system_template_in_event_table = EventTemplate.objects.filter(
            name__in=['免费系统模板', 'Pro系统模板']
        ).exists()
        self.assertFalse(system_template_in_event_table)
        
        # 验证用户模板不会出现在SystemTemplate表中
        user_template_in_system_table = SystemTemplate.objects.filter(
            name='用户模板'
        ).exists()
        self.assertFalse(user_template_in_system_table)
    
    def test_requirement_3_system_templates_not_modifiable_in_user_library(self):
        """要求3：系统模板不能在个人模板库中进行修改"""
        
        # 验证系统模板没有creator字段（不属于任何用户）
        self.assertFalse(hasattr(SystemTemplate, 'creator'))
        
        # 验证用户只能获取自己创建的用户模板进行修改
        user_templates = EventTemplate.objects.filter(creator=self.free_user)
        self.assertEqual(user_templates.count(), 1)
        self.assertEqual(user_templates.first().name, '用户模板')
        
        # 验证用户不能通过EventTemplate API修改系统模板
        # 系统模板的ID格式是 'system_X'，而用户模板是 'user_X'
        system_template_id = f'system_{self.free_system_template.id}'
        user_template_id = f'user_{self.user_template.id}'
        
        # 验证TemplateManager正确区分模板类型
        system_template, system_type = TemplateManager.get_template_by_id(
            system_template_id, self.free_user
        )
        self.assertEqual(system_type, 'system')
        self.assertIsInstance(system_template, SystemTemplate)
        
        user_template, user_type = TemplateManager.get_template_by_id(
            user_template_id, self.free_user
        )
        self.assertEqual(user_type, 'user')
        self.assertIsInstance(user_template, EventTemplate)
    
    def test_system_template_immutability(self):
        """测试系统模板的不可变性"""
        
        # 系统模板应该没有提供修改接口
        # 这里我们验证系统模板的关键属性
        
        # 验证系统模板没有steps关系（不能像用户模板那样添加EventStep）
        self.assertFalse(hasattr(self.free_system_template, 'steps'))
        
        # 验证系统模板使用template_config存储步骤信息
        self.assertTrue(hasattr(self.free_system_template, 'template_config'))
        self.assertIsInstance(self.free_system_template.template_config, dict)
        
        # 验证用户模板有steps关系（可以添加EventStep）
        self.assertTrue(hasattr(self.user_template, 'steps'))
    
    def test_template_id_format_consistency(self):
        """测试模板ID格式的一致性"""
        
        templates = TemplateManager.get_available_templates_for_user(self.free_user)
        
        for template in templates:
            if template['type'] == 'system':
                # 系统模板ID应该以'system_'开头
                self.assertTrue(template['id'].startswith('system_'))
                self.assertIn('system_template_id', template)
            elif template['type'] == 'user':
                # 用户模板ID应该以'user_'开头
                self.assertTrue(template['id'].startswith('user_'))
                self.assertIn('event_template_id', template)
    
    def test_system_template_access_control(self):
        """测试系统模板的访问控制"""
        
        # 测试订阅等级访问控制
        self.assertTrue(self.free_system_template.is_accessible_by_user(self.free_user))
        self.assertTrue(self.free_system_template.is_accessible_by_user(self.pro_user))
        self.assertTrue(self.free_system_template.is_accessible_by_user(self.max_user))
        
        self.assertFalse(self.pro_system_template.is_accessible_by_user(self.free_user))
        self.assertTrue(self.pro_system_template.is_accessible_by_user(self.pro_user))
        self.assertTrue(self.pro_system_template.is_accessible_by_user(self.max_user))
    
    def test_system_template_caching(self):
        """测试系统模板的缓存机制"""
        
        # 第一次获取（应该从数据库获取）
        templates1 = TemplateManager._get_system_templates_for_user(self.free_user)
        
        # 第二次获取（应该从缓存获取）
        templates2 = TemplateManager._get_system_templates_for_user(self.free_user)
        
        # 结果应该相同
        self.assertEqual(len(templates1), len(templates2))
        self.assertEqual(
            [t['name'] for t in templates1],
            [t['name'] for t in templates2]
        )


class SystemTemplateAPITest(APITestCase):
    """系统模板API测试"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='apiuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.system_template = SystemTemplate.objects.create(
            name='API测试系统模板',
            description='用于API测试的系统模板',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={'steps': []}
        )
        
        self.client = Client()
    
    def test_system_templates_in_api_response(self):
        """测试API响应中包含系统模板"""
        # 登录获取token
        login_response = self.client.post('/api/users/login/', {
            'username': 'apiuser',
            'password': 'testpass123'
        })
        token = login_response.json()['access']
        
        # 获取模板列表
        response = self.client.get('/api/templates/', 
                                 HTTP_AUTHORIZATION=f'Bearer {token}')
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # 验证响应包含系统模板
        self.assertIn('templates', data)
        templates = data['templates']
        
        system_templates = [t for t in templates if t['type'] == 'system']
        self.assertGreater(len(system_templates), 0)
        
        # 验证系统模板格式
        for template in system_templates:
            self.assertTrue(template['id'].startswith('system_'))
            self.assertEqual(template['creator_username'], 'System')
            self.assertIn('required_subscription', template)


if __name__ == '__main__':
    import django
    import os
    
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
    django.setup()
    
    import unittest
    unittest.main()
