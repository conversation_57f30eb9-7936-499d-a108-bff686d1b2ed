"""
测试添加环节后的WebSocket广播功能

这个测试验证房主添加新环节后，WebSocket广播机制是否正常工作
"""

import json
import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock

from core.models import Room, RoomState, RoomEventStep
from events.models import EventTemplate, EventStep

User = get_user_model()


class AddStepWebSocketBroadcastTest(APITestCase):
    """测试添加环节的WebSocket广播功能"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        # 设置为Pro用户以便测试付费环节
        self.user.subscription_level = User.SUBSCRIPTION_PRO
        self.user.save()
        
        # 创建模板
        self.template = EventTemplate.objects.create(
            name='测试模板',
            description='测试用模板',
            creator=self.user
        )
        
        # 创建模板步骤
        EventStep.objects.create(
            template=self.template,
            order=1,
            name='第一个环节',
            step_type=EventStep.STEP_FREE_CHAT,
            duration=300
        )
        
        # 创建房间
        self.room = Room.objects.create(
            room_code='TEST01',
            name='测试房间',
            host=self.user,
            event_template=self.template,
            status=RoomState.READY
        )
        
        # 复制模板步骤到房间
        from core.models import TemplateManager
        TemplateManager.copy_template_steps_to_room(self.template, 'user', self.room)

        # 确保房间状态正确
        self.room.refresh_from_db()

        # 登录用户
        self.client.force_authenticate(user=self.user)

    @patch('channels.layers.get_channel_layer')
    def test_add_step_websocket_broadcast(self, mock_get_channel_layer):
        """测试添加环节时的WebSocket广播"""
        # 模拟channel layer
        mock_channel_layer = MagicMock()
        # 设置group_send为同步方法
        mock_channel_layer.group_send = MagicMock()
        mock_get_channel_layer.return_value = mock_channel_layer
        
        # 添加环节
        url = f'/api/rooms/{self.room.room_code}/add-step/'
        data = {
            'step_type': EventStep.STEP_PAUSE,
            'name': '新添加的暂停环节',
            'duration': 180
        }
        
        response = self.client.post(url, data, format='json')

        # 调试信息
        if response.status_code != status.HTTP_201_CREATED:
            print(f"Response status: {response.status_code}")
            print(f"Response data: {response.data}")
            print(f"Room host: {self.room.host}")
            print(f"Authenticated user: {self.user}")
            print(f"Room status: {self.room.status}")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证WebSocket广播被调用
        self.assertTrue(mock_channel_layer.group_send.called)
        
        # 获取广播调用
        calls = mock_channel_layer.group_send.call_args_list
        self.assertGreaterEqual(len(calls), 1)  # 至少应该有一个广播调用

        # 验证第一个广播（环节添加）
        first_call = calls[0]
        group_name, message = first_call[0]
        
        self.assertEqual(group_name, f"room_{self.room.room_code}")
        self.assertEqual(message['type'], 'broadcast_step_added')
        self.assertIn('payload', message)
        
        payload = message['payload']
        self.assertIn('message', payload)
        self.assertIn('step', payload)
        self.assertEqual(payload['room_status'], RoomState.READY)
        self.assertEqual(payload['total_steps'], 2)  # 原来1个 + 新添加1个
        
        step_data = payload['step']
        self.assertEqual(step_data['name'], '新添加的暂停环节')
        self.assertEqual(step_data['step_type'], EventStep.STEP_PAUSE)
        self.assertEqual(step_data['duration'], 180)
        self.assertEqual(step_data['order'], 2)

        # 如果有第二个广播调用，验证它（房间条件状态更新）
        if len(calls) > 1:
            second_call = calls[1]
            group_name, message = second_call[0]

            self.assertEqual(group_name, f"room_{self.room.room_code}")
            self.assertEqual(message['type'], 'broadcast_room_condition_update')
            self.assertIn('payload', message)

    @patch('channels.layers.get_channel_layer')
    def test_add_step_broadcast_failure_handling(self, mock_get_channel_layer):
        """测试WebSocket广播失败时的处理"""
        # 模拟channel layer抛出异常
        mock_channel_layer = MagicMock()
        mock_channel_layer.group_send.side_effect = Exception("广播失败")
        mock_get_channel_layer.return_value = mock_channel_layer
        
        # 添加环节
        url = f'/api/rooms/{self.room.room_code}/add-step/'
        data = {
            'step_type': EventStep.STEP_PAUSE,
            'name': '新添加的暂停环节',
            'duration': 180
        }
        
        # 即使广播失败，API调用也应该成功
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证环节仍然被添加到数据库
        self.assertEqual(self.room.event_steps.count(), 2)
        new_step = self.room.event_steps.get(order=2)
        self.assertEqual(new_step.name, '新添加的暂停环节')

    def test_add_step_without_websocket(self):
        """测试在没有WebSocket的情况下添加环节"""
        # 不模拟channel layer，让它返回None
        with patch('channels.layers.get_channel_layer', return_value=None):
            url = f'/api/rooms/{self.room.room_code}/add-step/'
            data = {
                'step_type': EventStep.STEP_PAUSE,
                'name': '新添加的暂停环节',
                'duration': 180
            }
            
            response = self.client.post(url, data, format='json')
            self.assertEqual(response.status_code, status.HTTP_201_CREATED)
            
            # 验证环节被正确添加
            self.assertEqual(self.room.event_steps.count(), 2)
            new_step = self.room.event_steps.get(order=2)
            self.assertEqual(new_step.name, '新添加的暂停环节')


class RoomConditionStatusTest(TestCase):
    """测试房间条件状态同步方法"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.room = Room.objects.create(
            room_code='TEST01',
            name='测试房间',
            host=self.user,
            status=RoomState.READY
        )

    def test_get_room_condition_status_sync(self):
        """测试同步获取房间条件状态"""
        from core.services.room_manager import room_manager
        
        result = room_manager.get_room_condition_status_sync(self.room.room_code)
        
        self.assertIn('room_status', result)
        self.assertIn('is_ready_for_next_step', result)
        self.assertIn('condition_message', result)
        self.assertEqual(result['room_status'], RoomState.READY)

    def test_get_room_condition_status_sync_nonexistent_room(self):
        """测试获取不存在房间的条件状态"""
        from core.services.room_manager import room_manager
        
        result = room_manager.get_room_condition_status_sync('NONEXISTENT')
        
        self.assertIsNone(result['room_status'])
        self.assertFalse(result['is_ready_for_next_step'])
        self.assertEqual(result['condition_message'], '房间不存在')
