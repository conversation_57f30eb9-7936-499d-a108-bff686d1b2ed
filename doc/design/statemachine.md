### 房间状态机
|           |是否在房间开始时间之后|是否有人加入|是否满足下一环节开始条件|备注|
|---        |:---:|:---:|:---:|:---:|
|SCHEDULED  |F|/|/|为预约房间预留的状态|
|OPEN       |T|F|/|/|
|WAITING    |T|T|F|/|
|READY      |T|T|T|/|
|IN_PROGRESS|T|T|/|正在执行环节逻辑|
|ENDED      |F|/|/|/|
|CLOSED     |F|/|/|/

### 切换状态机的规则：

##### SCHEDULED：
- SCHEDULED -> OPEN：到达房间的开始时间

##### OPEN：
- OPEN -> WAITING：有人加入房间
- OPEN -> ENDED：超过房间结束时间

##### WAITING：
- WAITING -> READY：满足下一环节开始条件
- WAITING -> OPEN：最后一个人离开房间
- WAITING -> ENDED：超过房间结束时间

##### READY：
- READY -> IN_PROGRESS：房主开始下一次序的环节逻辑
- READY -> WAITING：下一环节开始条件被破坏
- READY -> ENDED：超过房间结束时间

##### IN_PROGRESS：
- IN_PROGRESS -> READY：环节结束
- IN_PROGRESS -> ENDED：超过房间结束时间

##### ENDED：
- ENDED -> CLOSED：超过房间结束时间15分钟

##### CLOSED：

