/**
 * 导航系统测试
 * 
 * 测试新的底部导航栏系统是否正常工作
 * 验证所有页面是否能正确加载和导航
 */

import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import { AppNavigator } from '../src/navigation/AppNavigator';
import { AuthProvider } from '../src/auth/AuthContext';
import { SubscriptionProvider } from '../src/contexts/SubscriptionContext';

// Mock dependencies
jest.mock('../src/auth/AuthContext', () => ({
  AuthProvider: ({ children }) => children,
  useAuth: () => ({
    user: { username: 'testuser', email: '<EMAIL>' },
    token: 'mock-token',
    logout: jest.fn(),
  }),
}));

jest.mock('../src/contexts/SubscriptionContext', () => ({
  SubscriptionProvider: ({ children }) => children,
  useSubscription: () => ({
    subscriptionInfo: { current_level: 'Free' },
  }),
}));

jest.mock('../src/contexts/ReservationContext', () => ({
  useCalendarData: () => ({
    calendarData: [],
    loading: false,
    error: null,
    loadCalendarData: jest.fn(),
    refreshCalendarData: jest.fn(),
  }),
  useReservationDetail: () => ({
    selectedReservation: null,
    selectReservation: jest.fn(),
  }),
}));

jest.mock('../src/api/eventApi', () => ({
  getEventTemplates: jest.fn().mockResolvedValue([]),
}));

jest.mock('../src/api/checkInApi', () => ({
  getCheckInStatus: jest.fn().mockResolvedValue({ month_check_in_dates: [] }),
  hasCheckedInOnDate: jest.fn().mockReturnValue(false),
}));

const TestWrapper = ({ children }) => (
  <NavigationContainer>
    <AuthProvider>
      <SubscriptionProvider>
        {children}
      </SubscriptionProvider>
    </AuthProvider>
  </NavigationContainer>
);

describe('Navigation System', () => {
  it('should render bottom tab navigator', () => {
    const { getByText } = render(
      <TestWrapper>
        <AppNavigator />
      </TestWrapper>
    );

    // 检查底部导航栏的标签是否存在
    expect(getByText('房间')).toBeTruthy();
    expect(getByText('环节')).toBeTruthy();
    expect(getByText('日历')).toBeTruthy();
    expect(getByText('设置')).toBeTruthy();
  });

  it('should navigate between tabs', () => {
    const { getByText } = render(
      <TestWrapper>
        <AppNavigator />
      </TestWrapper>
    );

    // 点击环节标签
    fireEvent.press(getByText('环节'));
    
    // 验证是否导航到环节页面
    expect(getByText('新建模板')).toBeTruthy();

    // 点击日历标签
    fireEvent.press(getByText('日历'));
    
    // 验证是否导航到日历页面
    expect(getByText('新建预约')).toBeTruthy();

    // 点击设置标签
    fireEvent.press(getByText('设置'));
    
    // 验证是否导航到设置页面
    expect(getByText('账户设置')).toBeTruthy();
  });

  it('should display user information in settings', () => {
    const { getByText } = render(
      <TestWrapper>
        <AppNavigator />
      </TestWrapper>
    );

    // 导航到设置页面
    fireEvent.press(getByText('设置'));
    
    // 验证用户信息是否显示
    expect(getByText('testuser')).toBeTruthy();
    expect(getByText('<EMAIL>')).toBeTruthy();
  });

  it('should show create room functionality in home screen', () => {
    const { getByText } = render(
      <TestWrapper>
        <AppNavigator />
      </TestWrapper>
    );

    // 确保在房间页面
    fireEvent.press(getByText('房间'));
    
    // 验证创建房间功能是否存在
    expect(getByText('创建房间')).toBeTruthy();
    expect(getByText('加入')).toBeTruthy();
  });
});

describe('Page Consistency', () => {
  it('should use consistent styling across all pages', () => {
    const { getByText, getByTestId } = render(
      <TestWrapper>
        <AppNavigator />
      </TestWrapper>
    );

    // 测试每个页面是否都使用了Screen组件
    const pages = ['房间', '环节', '日历', '设置'];
    
    pages.forEach(page => {
      fireEvent.press(getByText(page));
      // 验证页面是否正确渲染（没有崩溃）
      expect(getByText(page)).toBeTruthy();
    });
  });

  it('should have proper animation effects', () => {
    const { getByText } = render(
      <TestWrapper>
        <AppNavigator />
      </TestWrapper>
    );

    // 验证动画组件是否存在（通过检查Animated.View的存在）
    // 这里我们主要确保页面能正常渲染，动画效果在实际使用中验证
    expect(getByText('房间')).toBeTruthy();
  });
});
