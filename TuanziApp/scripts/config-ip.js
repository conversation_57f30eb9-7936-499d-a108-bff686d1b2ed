const os = require('os');
const fs = require('fs');
const path = require('path');

// Function to find the local IPv4 address
function getLocalIp() {
  // Check if we're targeting emulator or physical device
  const targetDevice = process.env.TARGET_DEVICE || 'emulator';

  if (targetDevice === 'emulator') {
    // For Android emulator, use ******** to access host
    return '********';
  } else {
    // For physical devices, use actual network IP
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
      for (const iface of interfaces[name]) {
        // Skip over internal (i.e. 127.0.0.1) and non-ipv4 addresses
        if (iface.family === 'IPv4' && !iface.internal) {
          return iface.address;
        }
      }
    }
    return '********'; // Fallback for Android emulator
  }
}

const localIp = getLocalIp();
const configContent = `// This file is auto-generated. Do not edit.
export const YOUR_COMPUTER_IP = '${localIp}';
`;

const configPath = path.join(__dirname, '..', 'src', 'api', 'config.ts');

fs.writeFileSync(configPath, configContent);

console.log(`IP address configured to: ${localIp} in ${configPath}`);
