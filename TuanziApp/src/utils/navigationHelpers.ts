// TuanziApp/src/utils/navigationHelpers.ts

import { NavigationProp } from '@react-navigation/native';
import { RootStackParamList } from '../types';

/**
 * 导航辅助工具
 */

/**
 * 安全地导航到指定页面
 * @param navigation 导航对象
 * @param screenName 目标页面名称
 * @param params 页面参数
 */
export const safeNavigate = (
  navigation: NavigationProp<RootStackParamList>,
  screenName: keyof RootStackParamList,
  params?: any
) => {
  try {
    navigation.navigate(screenName as never, params as never);
  } catch (error) {
    console.error('Navigation error:', error);
  }
};

/**
 * 安全地返回上一页
 * @param navigation 导航对象
 * @param fallbackScreen 如果无法返回时的备用页面
 */
export const safeGoBack = (
  navigation: NavigationProp<RootStackParamList>,
  fallbackScreen?: keyof RootStackParamList
) => {
  try {
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else if (fallbackScreen) {
      navigation.navigate(fallbackScreen as never);
    }
  } catch (error) {
    console.error('Go back error:', error);
    if (fallbackScreen) {
      navigation.navigate(fallbackScreen as never);
    }
  }
};

/**
 * 重置导航栈到指定页面
 * @param navigation 导航对象
 * @param screenName 目标页面名称
 * @param params 页面参数
 */
export const resetToScreen = (
  navigation: NavigationProp<RootStackParamList>,
  screenName: keyof RootStackParamList,
  params?: any
) => {
  try {
    navigation.reset({
      index: 0,
      routes: [{ name: screenName as never, params }],
    });
  } catch (error) {
    console.error('Reset navigation error:', error);
  }
};

/**
 * 返回到导航栈中的特定页面
 * 如果该页面在栈中，则返回到该页面；否则导航到该页面
 * @param navigation 导航对象
 * @param targetScreen 目标页面名称
 * @param params 页面参数（仅在页面不在栈中时使用）
 */
export const navigateBackToScreen = (
  navigation: NavigationProp<RootStackParamList>,
  targetScreen: keyof RootStackParamList,
  params?: any
) => {
  try {
    const state = navigation.getState();
    const targetIndex = state.routes.findIndex(route => route.name === targetScreen);

    if (targetIndex !== -1) {
      // 如果目标页面在栈中，计算需要返回的次数
      const popCount = state.index - targetIndex;
      if (popCount > 0) {
        // 连续调用goBack返回到目标页面
        for (let i = 0; i < popCount; i++) {
          navigation.goBack();
        }
      }
      // 如果popCount为0，说明已经在目标页面了
    } else {
      // 如果目标页面不在栈中，直接导航到该页面
      navigation.navigate(targetScreen as never, params as never);
    }
  } catch (error) {
    console.error('Navigate back to screen error:', error);
    // 出错时直接导航到目标页面
    navigation.navigate(targetScreen as never, params as never);
  }
};
