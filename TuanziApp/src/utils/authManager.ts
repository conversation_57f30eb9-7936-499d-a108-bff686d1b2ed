// TuanziApp/src/utils/authManager.ts

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

export interface AuthResponse {
  error?: string;
  code?: string;
  redirect_to_login?: boolean;
  timeout_minutes?: number;
}

export class AuthManager {
  private static instance: AuthManager;
  private onAuthExpired?: () => void;
  private sessionCheckInterval?: NodeJS.Timeout;
  private isLoggingOut: boolean = false;
  
  private constructor() {}
  
  static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }
  
  /**
   * 设置认证过期回调
   */
  setOnAuthExpired(callback: () => void) {
    this.onAuthExpired = callback;
  }
  
  /**
   * 开始会话检查
   */
  startSessionCheck() {
    this.isLoggingOut = false; // 重置登出标志
    // 每5分钟检查一次会话状态
    this.sessionCheckInterval = setInterval(() => {
      this.checkSessionStatus();
    }, 5 * 60 * 1000);
  }
  
  /**
   * 停止会话检查
   */
  stopSessionCheck() {
    this.isLoggingOut = true;
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = undefined;
    }
  }
  
  /**
   * 检查会话状态
   */
  private async checkSessionStatus() {
    // 如果正在登出，跳过会话检查
    if (this.isLoggingOut) {
      return;
    }

    try {
      const token = await AsyncStorage.getItem('access_token');
      if (!token) {
        this.handleAuthExpired('No token found');
        return;
      }

      // 发送一个轻量级的API请求来检查token状态
      // 需要使用完整的API URL
      const { API_URL } = require('../api/client');
      const response = await fetch(`${API_URL}/api/health-check/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.status === 401) {
        const data = await response.json();
        this.handleAuthResponse(data);
      }
    } catch (error) {
      console.warn('Session check failed:', error);
    }
  }
  
  /**
   * 处理API响应中的认证错误
   */
  handleAuthResponse(response: AuthResponse) {
    if (response.redirect_to_login) {
      switch (response.code) {
        case 'SESSION_TIMEOUT':
          this.handleSessionTimeout(response.timeout_minutes);
          break;
        case 'TOKEN_INVALID':
          this.handleTokenInvalid();
          break;
        case 'AUTH_REQUIRED':
          this.handleAuthRequired();
          break;
        default:
          this.handleAuthExpired(response.error || 'Authentication failed');
      }
    }
  }
  
  /**
   * 处理会话超时
   */
  private handleSessionTimeout(timeoutMinutes?: number) {
    const message = timeoutMinutes
      ? `由于 ${timeoutMinutes} 分钟无活动，您的会话已过期。请重新登录。`
      : '由于长时间无活动，您的会话已过期。请重新登录。';

    // 直接调用重定向，不显示Alert，因为会自动跳转到登录页面
    this.redirectToLogin();
  }

  /**
   * 处理无效token
   */
  private handleTokenInvalid() {
    // 直接调用重定向，不显示Alert，因为会自动跳转到登录页面
    this.redirectToLogin();
  }

  /**
   * 处理需要认证
   */
  private handleAuthRequired() {
    // 直接调用重定向，不显示Alert，因为会自动跳转到登录页面
    this.redirectToLogin();
  }

  /**
   * 处理通用认证过期
   */
  private handleAuthExpired(message: string) {
    // 直接调用重定向，不显示Alert，因为会自动跳转到登录页面
    this.redirectToLogin();
  }
  
  /**
   * 重定向到登录页面
   */
  private redirectToLogin() {
    // 清除本地存储的认证信息
    this.clearAuthData();
    
    // 调用认证过期回调
    if (this.onAuthExpired) {
      this.onAuthExpired();
    }
  }
  
  /**
   * 清除认证数据
   */
  private async clearAuthData() {
    try {
      // 使用与AuthContext相同的key名称
      await AsyncStorage.multiRemove([
        'access_token',
        'refresh_token',
        'user_data',
        'auth_token', // AuthContext使用的key
      ]);
    } catch (error) {
      console.error('Failed to clear auth data:', error);
    }
  }
  
  /**
   * 检查API响应是否包含认证错误
   */
  static checkApiResponse(response: any): boolean {
    if (response && response.redirect_to_login) {
      AuthManager.getInstance().handleAuthResponse(response);
      return true;
    }
    return false;
  }
}

/**
 * JWT Token刷新机制
 */
class TokenRefreshManager {
  private static instance: TokenRefreshManager;
  private refreshPromise: Promise<string | null> | null = null;

  static getInstance(): TokenRefreshManager {
    if (!TokenRefreshManager.instance) {
      TokenRefreshManager.instance = new TokenRefreshManager();
    }
    return TokenRefreshManager.instance;
  }

  /**
   * 刷新访问令牌
   */
  async refreshAccessToken(): Promise<string | null> {
    // 如果已经有刷新请求在进行中，等待它完成
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();
    const result = await this.refreshPromise;
    this.refreshPromise = null;
    return result;
  }

  private async performTokenRefresh(): Promise<string | null> {
    try {
      const refreshToken = await AsyncStorage.getItem('refresh_token');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const { API_URL } = require('../api/client');
      const response = await fetch(`${API_URL}/api/users/token/refresh/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh: refreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        const newAccessToken = data.access;

        // 存储新的访问令牌
        await AsyncStorage.setItem('access_token', newAccessToken);

        // 如果有新的刷新令牌，也要存储
        if (data.refresh) {
          await AsyncStorage.setItem('refresh_token', data.refresh);
        }

        console.log('Token refreshed successfully');
        return newAccessToken;
      } else {
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      // 刷新失败，清除所有令牌并重定向到登录
      await AsyncStorage.multiRemove(['access_token', 'refresh_token', 'userToken']);
      AuthManager.getInstance().handleAuthExpired('Token refresh failed');
      return null;
    }
  }
}

/**
 * API请求拦截器 - 支持自动token刷新
 */
export const createAuthenticatedFetch = (originalFetch: typeof fetch) => {
  return async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    try {
      let response = await originalFetch(input, init);

      // 检查认证错误
      if (response.status === 401) {
        try {
          const data = await response.clone().json();

          // 尝试刷新token
          const newToken = await TokenRefreshManager.getInstance().refreshAccessToken();

          if (newToken && init?.headers) {
            // 更新请求头中的token并重试请求
            const headers = new Headers(init.headers);
            headers.set('Authorization', `Bearer ${newToken}`);

            const retryInit = {
              ...init,
              headers: headers,
            };

            response = await originalFetch(input, retryInit);

            // 如果重试后仍然是401，则处理认证失败
            if (response.status === 401) {
              AuthManager.getInstance().handleAuthResponse(data);
            }
          } else {
            AuthManager.getInstance().handleAuthResponse(data);
          }
        } catch (e) {
          // 如果无法解析JSON，使用默认处理
          AuthManager.getInstance().handleAuthExpired('Authentication failed');
        }
      }

      return response;
    } catch (error) {
      throw error;
    }
  };
};

// 导出单例实例
export const authManager = AuthManager.getInstance();
