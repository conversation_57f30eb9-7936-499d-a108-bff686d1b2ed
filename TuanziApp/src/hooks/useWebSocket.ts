/**
 * 团子APP WebSocket连接管理Hook
 *
 * 这是一个自定义React Hook，提供WebSocket连接的完整管理功能。
 * 专为团子APP的实时房间通信设计，支持自动重连、心跳检测、网络状态监听等功能。
 *
 * 主要功能：
 * 1. WebSocket连接管理：建立、维护、断开连接
 * 2. 自动重连机制：网络异常时自动尝试重连
 * 3. 心跳检测：定期发送心跳包保持连接活跃
 * 4. 网络状态监听：监听网络变化并相应处理
 * 5. 消息收发：提供统一的消息发送接口
 * 6. 错误处理：完整的错误处理和用户提示
 *
 * 技术特性：
 * - 支持手动和自动连接模式
 * - 可配置的重连策略和延迟
 * - 内存泄漏防护（清理定时器和引用）
 * - TypeScript类型安全
 * - 与React生命周期集成
 *
 * 使用场景：
 * - 房间实时通信
 * - 游戏状态同步
 * - 聊天消息传递
 * - 绘图数据同步
 *
 * @example
 * const { isConnected, sendMessage, connect } = useWebSocket({
 *   onMessage: handleMessage,
 *   maxReconnectAttempts: 5,
 *   autoConnect: false
 * });
 */

import { useRef, useCallback, useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { WEBSOCKET_URL_BASE } from '../api/client';
import { WSMessage } from '../types';
import { useNetworkStatus } from './useNetworkStatus';

/**
 * WebSocket Hook配置选项接口
 */
interface UseWebSocketOptions {
  roomCode?: string;                                    // 房间代码，用于连接特定房间
  token?: string;                                       // JWT认证令牌
  onMessage?: (message: WSMessage) => void;             // 消息接收回调函数
  maxReconnectAttempts?: number;                        // 最大重连尝试次数，默认5次
  reconnectDelay?: number;                              // 重连延迟时间（毫秒），默认3000ms
  autoConnect?: boolean;                                // 是否自动连接，默认true
}

/**
 * WebSocket Hook返回值接口
 */
interface UseWebSocketReturn {
  isConnected: boolean;                                 // 连接状态
  sendMessage: (action: string, payload?: Record<string, unknown>) => void;  // 发送消息方法
  reconnect: () => void;                                // 手动重连方法
  disconnect: () => void;                               // 断开连接方法
  connect: (roomCode: string, token: string) => void;   // 手动连接方法
}

export const useWebSocket = ({
  roomCode,
  token,
  onMessage,
  maxReconnectAttempts = 5,
  reconnectDelay = 3000, // 增加重连延迟到3秒
  autoConnect = true,
}: UseWebSocketOptions): UseWebSocketReturn => {
  const ws = useRef<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 网络状态管理
  const [hasShownOfflineAlert, setHasShownOfflineAlert] = useState(false);
  const [wasConnectedBefore, setWasConnectedBefore] = useState(false);
  const lastAlertTime = useRef<number>(0);

  // 跟踪当前连接参数
  const connectionParams = useRef<{ roomCode: string; token: string } | null>(null);

  // 网络状态监听
  const { isInternetReachable } = useNetworkStatus({
    onNetworkRestore: () => {
      // 网络恢复时，如果之前连接失败且有连接参数，尝试重连
      if (!isConnected && connectionParams.current && reconnectAttempts >= maxReconnectAttempts) {
        console.log('网络恢复，尝试重新连接WebSocket');
        setReconnectAttempts(0);
        setHasShownOfflineAlert(false);
        connect(connectionParams.current.roomCode, connectionParams.current.token);
      }
    },
    checkInterval: 10000, // 10秒检查一次网络状态
  });

  // 心跳机制
  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = setInterval(() => {
      if (ws.current?.readyState === WebSocket.OPEN) {
        const heartbeatMessage = JSON.stringify({ action: 'heartbeat', payload: {} });
        ws.current.send(heartbeatMessage);
      }
    }, 30000); // 每30秒发送一次心跳
  }, []);

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }
  }, []);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    stopHeartbeat();

    if (ws.current) {
      ws.current.close(1000, 'Manual disconnect');
      ws.current = null;
    }

    setIsConnected(false);
    setReconnectAttempts(0);
  }, [stopHeartbeat]);

  const connect = useCallback((connectRoomCode?: string, connectToken?: string) => {
    const finalRoomCode = connectRoomCode || roomCode;
    const finalToken = connectToken || token;

    if (!finalRoomCode || !finalToken) {
      console.warn('Cannot connect: missing roomCode or token');
      return;
    }

    // 更新连接参数
    connectionParams.current = { roomCode: finalRoomCode, token: finalToken };

    // Clean up existing connection
    if (ws.current) {
      ws.current.close();
    }

    const wsUrl = `${WEBSOCKET_URL_BASE}/ws/room/${finalRoomCode}/?token=${finalToken}`;
    console.log('Connecting to WebSocket:', wsUrl);

    ws.current = new WebSocket(wsUrl);

    ws.current.onopen = () => {
      console.log('WebSocket connected');
      setIsConnected(true);
      setReconnectAttempts(0);
      setWasConnectedBefore(true);

      // 启动心跳机制
      startHeartbeat();

      // 如果之前显示过离线提示，现在连接成功了，重置状态
      if (hasShownOfflineAlert) {
        setHasShownOfflineAlert(false);
        console.log('网络已恢复，重置离线提示状态');
      }
    };

    ws.current.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      setIsConnected(false);
      stopHeartbeat();

      // Only attempt reconnection for unexpected closures
      if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
        const delay = Math.min(reconnectDelay * Math.pow(2, reconnectAttempts), 30000);
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts + 1}/${maxReconnectAttempts})`);

        reconnectTimeoutRef.current = setTimeout(() => {
          setReconnectAttempts(prev => prev + 1);
          if (connectionParams.current) {
            connect(connectionParams.current.roomCode, connectionParams.current.token);
          }
        }, delay) as any;
      } else if (reconnectAttempts >= maxReconnectAttempts) {
        console.error('Max reconnection attempts reached');

        // 优化弹窗逻辑：降低弹窗频率，避免重复弹窗
        const now = Date.now();
        const minAlertInterval = 60000; // 最少1分钟间隔

        if (!hasShownOfflineAlert && wasConnectedBefore && (now - lastAlertTime.current) > minAlertInterval) {
          setHasShownOfflineAlert(true);
          lastAlertTime.current = now;

          Alert.alert(
            '网络连接中断',
            '无法连接到服务器，请检查网络连接。网络恢复后将自动重连。',
            [{
              text: '确定',
              onPress: () => {
                // 用户确认后，允许下次重连时重新显示提示
                setTimeout(() => {
                  setHasShownOfflineAlert(false);
                }, 30000); // 30秒后允许再次显示
              }
            }]
          );
        }
      }
    };

    ws.current.onerror = (error: any) => {
      console.error('WebSocket error:', error.message || error);
    };

    ws.current.onmessage = (event) => {
      try {
        const message: WSMessage = JSON.parse(event.data);
        onMessage?.(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };
  }, [roomCode, token, onMessage, reconnectAttempts, maxReconnectAttempts, reconnectDelay, startHeartbeat, stopHeartbeat]);

  const sendMessage = useCallback((action: string, payload: Record<string, unknown> = {}) => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      const message = JSON.stringify({ action, payload });
      ws.current.send(message);
    } else {
      console.warn('Cannot send message: WebSocket not connected');
    }
  }, []);

  const reconnect = useCallback(() => {
    setReconnectAttempts(0);
    setHasShownOfflineAlert(false); // 重置弹窗状态
    if (connectionParams.current) {
      connect(connectionParams.current.roomCode, connectionParams.current.token);
    }
  }, [connect]);

  useEffect(() => {
    if (autoConnect && roomCode && token) {
      connect(roomCode, token);
    }

    return () => {
      disconnect();
    };
  }, [autoConnect, roomCode, token, connect, disconnect]);

  return {
    isConnected,
    sendMessage,
    reconnect,
    disconnect,
    connect,
  };
};
