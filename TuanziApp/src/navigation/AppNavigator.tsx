import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Text, View, StyleSheet } from 'react-native';
import { HomeScreen } from '../screens/HomeScreen';
import { RoomScreen } from '../screens/RoomScreen';
import { CreateRoomScreen } from '../screens/CreateRoomScreen';
import { TemplateListScreen } from '../screens/EventDesigner/TemplateListScreen';
import { CreateTemplateScreen } from '../screens/EventDesigner/CreateTemplateScreen';
import { TemplateDetailScreen } from '../screens/EventDesigner/TemplateDetailScreen';
import { AddStepScreen } from '../screens/EventDesigner/AddStepScreen';
import { EditStepScreen } from '../screens/EventDesigner/EditStepScreen';
import { SubscriptionScreen } from '../screens/SubscriptionScreen';
import { CalendarScreen } from '../screens/CalendarScreen';
import { ScheduleRoomScreen } from '../screens/ScheduleRoomScreen';
import { RootStackParamList, TabParamList } from '../types';
import { theme } from '../styles/theme';

// 创建导航器
const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

// 设置页面组件（临时）
const SettingsScreen = () => (
  <View style={styles.placeholderContainer}>
    <Text style={styles.placeholderText}>设置页面</Text>
    <Text style={styles.placeholderSubtext}>即将推出</Text>
  </View>
);

// 自定义图标组件
const TabIcon = ({ emoji, focused }: { emoji: string; focused: boolean }) => (
  <Text style={[styles.tabIcon, { opacity: focused ? 1 : 0.6 }]}>
    {emoji}
  </Text>
);

// 底部导航栏
const TabNavigator = () => (
  <Tab.Navigator
    screenOptions={{
      tabBarStyle: styles.tabBar,
      tabBarActiveTintColor: theme.colors.primary,
      tabBarInactiveTintColor: theme.colors.textTertiary,
      tabBarLabelStyle: styles.tabLabel,
      headerStyle: styles.header,
      headerTitleStyle: styles.headerTitle,
      headerTintColor: theme.colors.textPrimary,
    }}
  >
    <Tab.Screen
      name="RoomTab"
      component={HomeScreen}
      options={{
        title: '房间',
        headerTitle: '团子',
        tabBarIcon: ({ focused }) => <TabIcon emoji="🏠" focused={focused} />,
      }}
    />
    <Tab.Screen
      name="EventDesignerTab"
      component={TemplateListScreen}
      options={{
        title: '环节',
        headerTitle: '环节设计器',
        tabBarIcon: ({ focused }) => <TabIcon emoji="🎨" focused={focused} />,
      }}
    />
    <Tab.Screen
      name="CalendarTab"
      component={CalendarScreen}
      options={{
        title: '日历',
        headerTitle: '日历预约',
        tabBarIcon: ({ focused }) => <TabIcon emoji="📅" focused={focused} />,
      }}
    />
    <Tab.Screen
      name="SettingsTab"
      component={SettingsScreen}
      options={{
        title: '设置',
        headerTitle: '设置',
        tabBarIcon: ({ focused }) => <TabIcon emoji="⚙️" focused={focused} />,
      }}
    />
  </Tab.Navigator>
);

// 主导航器（包含模态页面）
export const AppNavigator = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen
      name="MainTabs"
      component={TabNavigator}
    />
    <Stack.Screen
      name="CreateRoom"
      component={CreateRoomScreen}
      options={({ route }) => ({
        headerShown: true,
        title: route.params?.mode === 'select' ? '选择模板' : '创建房间',
        presentation: 'modal',
      })}
    />
    <Stack.Screen
      name="Room"
      component={RoomScreen}
      options={({ route }) => ({
        headerShown: true,
        title: `房间: ${route.params.room.room_code}`,
        presentation: 'card',
      })}
    />
    <Stack.Screen
      name="CreateTemplate"
      component={CreateTemplateScreen}
      options={{
        headerShown: true,
        title: '创建新模板',
        presentation: 'modal',
      }}
    />
    <Stack.Screen
      name="TemplateDetail"
      component={TemplateDetailScreen}
      options={{
        headerShown: true,
        title: '模板详情',
        presentation: 'card',
      }}
    />
    <Stack.Screen
      name="AddStep"
      component={AddStepScreen}
      options={{
        headerShown: true,
        title: '添加新步骤',
        presentation: 'modal',
      }}
    />
    <Stack.Screen
      name="EditStep"
      component={EditStepScreen}
      options={{
        headerShown: true,
        title: '编辑步骤',
        presentation: 'modal',
      }}
    />
    <Stack.Screen
      name="Subscription"
      component={SubscriptionScreen}
      options={{
        headerShown: true,
        title: '订阅管理',
        presentation: 'modal',
      }}
    />
    <Stack.Screen
      name="ScheduleRoom"
      component={ScheduleRoomScreen}
      options={{
        headerShown: true,
        title: '预约房间',
        presentation: 'modal',
      }}
    />
  </Stack.Navigator>
);

const styles = StyleSheet.create({
  tabBar: {
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingBottom: 8,
    paddingTop: 8,
    height: 70,
  },
  tabLabel: {
    fontSize: theme.typography.fontSize.xs,
    fontWeight: theme.typography.fontWeight.medium,
    marginTop: 4,
  },
  tabIcon: {
    fontSize: 24,
  },
  header: {
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    elevation: 0,
    shadowOpacity: 0,
  },
  headerTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  placeholderText: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.sm,
  },
  placeholderSubtext: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
  },
});
