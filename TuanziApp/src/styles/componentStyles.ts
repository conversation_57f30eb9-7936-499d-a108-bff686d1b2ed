/**
 * 统一的组件样式系统
 * 
 * 提供预定义的组件样式，确保整个应用的一致性
 */

import { StyleSheet } from 'react-native';
import { theme } from './theme';

/**
 * 通用按钮样式
 */
export const buttonStyles = StyleSheet.create({
  // 基础按钮样式
  base: {
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.xl,
    minHeight: 44,
    ...theme.shadows.md,
  },
  
  // 主要按钮
  primary: {
    backgroundColor: theme.colors.primary,
  },
  
  // 次要按钮
  secondary: {
    backgroundColor: theme.colors.secondary,
  },
  
  // 成功按钮
  success: {
    backgroundColor: theme.colors.success,
  },
  
  // 危险按钮
  danger: {
    backgroundColor: theme.colors.error,
  },
  
  // 禁用按钮
  disabled: {
    backgroundColor: theme.colors.gray300,
    ...theme.shadows.none,
  },
  
  // 按钮文本
  text: {
    color: theme.colors.white,
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  
  // 禁用按钮文本
  disabledText: {
    color: theme.colors.textTertiary,
  },
});

/**
 * 通用卡片样式
 */
export const cardStyles = StyleSheet.create({
  // 基础卡片
  base: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.lg,
    ...theme.shadows.md,
  },
  
  // 小卡片
  small: {
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  
  // 大卡片
  large: {
    padding: theme.spacing['2xl'],
    borderRadius: theme.borderRadius['2xl'],
    ...theme.shadows.lg,
  },
  
  // 带边框的卡片
  outlined: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    ...theme.shadows.sm,
  },
});

/**
 * 通用输入框样式
 */
export const inputStyles = StyleSheet.create({
  // 基础输入框
  base: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    backgroundColor: theme.colors.surface,
    minHeight: 44,
  },
  
  // 聚焦状态
  focused: {
    borderColor: theme.colors.primary,
    ...theme.shadows.sm,
  },
  
  // 错误状态
  error: {
    borderColor: theme.colors.error,
  },
  
  // 禁用状态
  disabled: {
    backgroundColor: theme.colors.gray100,
    color: theme.colors.textTertiary,
  },
});

/**
 * 通用文本样式
 */
export const textStyles = StyleSheet.create({
  // 标题样式
  h1: {
    fontSize: theme.typography.fontSize['5xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    lineHeight: theme.typography.fontSize['5xl'] * theme.typography.lineHeight.tight,
  },
  
  h2: {
    fontSize: theme.typography.fontSize['4xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    lineHeight: theme.typography.fontSize['4xl'] * theme.typography.lineHeight.tight,
  },
  
  h3: {
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
    lineHeight: theme.typography.fontSize['3xl'] * theme.typography.lineHeight.tight,
  },
  
  h4: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
    lineHeight: theme.typography.fontSize['2xl'] * theme.typography.lineHeight.normal,
  },
  
  // 正文样式
  body: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.normal,
  },
  
  bodySecondary: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
    lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.normal,
  },
  
  // 小文本
  caption: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    lineHeight: theme.typography.fontSize.sm * theme.typography.lineHeight.normal,
  },
  
  // 标签文本
  label: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.textPrimary,
  },
});

/**
 * 通用容器样式
 */
export const containerStyles = StyleSheet.create({
  // 页面容器
  page: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  
  // 内容容器
  content: {
    flex: 1,
    padding: theme.spacing.lg,
  },
  
  // 居中容器
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  
  // 行容器
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  // 列容器
  column: {
    flexDirection: 'column',
  },
  
  // 间距容器
  spaceBetween: {
    justifyContent: 'space-between',
  },
  
  spaceAround: {
    justifyContent: 'space-around',
  },
  
  spaceEvenly: {
    justifyContent: 'space-evenly',
  },
});

/**
 * 通用列表样式
 */
export const listStyles = StyleSheet.create({
  // 列表项
  item: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.sm,
    ...theme.shadows.sm,
  },
  
  // 列表项标题
  itemTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  
  // 列表项描述
  itemDescription: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  
  // 分隔线
  separator: {
    height: 1,
    backgroundColor: theme.colors.border,
    marginVertical: theme.spacing.sm,
  },
});

/**
 * 状态指示器样式
 */
export const statusStyles = StyleSheet.create({
  // 成功状态
  success: {
    backgroundColor: theme.colors.successSoft,
    borderColor: theme.colors.success,
    borderWidth: 1,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
  },
  
  // 警告状态
  warning: {
    backgroundColor: theme.colors.warningSoft,
    borderColor: theme.colors.warning,
    borderWidth: 1,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
  },
  
  // 错误状态
  error: {
    backgroundColor: theme.colors.errorSoft,
    borderColor: theme.colors.error,
    borderWidth: 1,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
  },
  
  // 信息状态
  info: {
    backgroundColor: theme.colors.infoSoft,
    borderColor: theme.colors.info,
    borderWidth: 1,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
  },
});
