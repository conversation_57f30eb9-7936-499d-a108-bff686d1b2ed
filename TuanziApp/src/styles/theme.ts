/**
 * 应用主题系统
 * 
 * 功能：
 * - 统一的颜色系统
 * - 字体大小和权重
 * - 间距系统
 * - 阴影样式
 * - 边框半径
 */

export const colors = {
  // 主色调 - 可信的深蓝紫色，既专业又有活力
  primary: '#6366F1',        // 现代感的靛蓝色，体现可信与活力
  primaryLight: '#818CF8',   // 柔和的浅色变体
  primaryDark: '#4F46E5',    // 深色变体，增强对比度
  primarySoft: '#E0E7FF',    // 极浅的背景色

  // 辅助色 - 活力色彩组合
  secondary: '#EC4899',      // 活力粉色，增加趣味性
  secondaryLight: '#F472B6',
  secondaryDark: '#DB2777',
  secondarySoft: '#FCE7F3',

  // 第三色调 - 温暖的橙色，营造友好氛围
  tertiary: '#F59E0B',       // 温暖橙色
  tertiaryLight: '#FBBF24',
  tertiaryDark: '#D97706',
  tertiarySoft: '#FEF3C7',

  // 活力色彩 - 用于徽章和游戏元素
  vibrant: {
    coral: '#FF6B6B',        // 珊瑚橙 - 用于重要提示
    mint: '#4ECDC4',         // 薄荷绿 - 用于成功状态
    lemon: '#FFE66D',        // 柠檬黄 - 用于警告和高亮
    lavender: '#A8E6CF',     // 薰衣草绿 - 用于次要信息
    peach: '#FFB3BA',        // 桃色 - 用于温和提示
    sky: '#87CEEB',          // 天空蓝 - 用于信息展示
  },

  // 状态色 - 保持专业感的同时增加活力
  success: '#10B981',
  successLight: '#34D399',
  successDark: '#059669',
  successSoft: '#D1FAE5',

  warning: '#F59E0B',
  warningLight: '#FBBF24',
  warningDark: '#D97706',
  warningSoft: '#FEF3C7',

  error: '#EF4444',
  errorLight: '#F87171',
  errorDark: '#DC2626',
  errorSoft: '#FEE2E2',

  info: '#3B82F6',
  infoLight: '#60A5FA',
  infoDark: '#2563EB',
  infoSoft: '#DBEAFE',

  // 中性色 - 更温暖的灰色调
  white: '#FFFFFF',
  black: '#1F2937',          // 稍微柔和的黑色

  gray50: '#FAFAFA',         // 更温暖的浅灰
  gray100: '#F5F5F5',
  gray200: '#EEEEEE',
  gray300: '#E0E0E0',
  gray400: '#BDBDBD',
  gray500: '#9E9E9E',
  gray600: '#757575',
  gray700: '#616161',
  gray800: '#424242',
  gray900: '#212121',

  // 背景色 - 更温暖舒适的背景
  background: '#FAFAFA',     // 温暖的浅灰背景
  surface: '#FFFFFF',
  surfaceElevated: '#FFFFFF', // 提升的表面（卡片等）

  // 文本色 - 更好的可读性
  textPrimary: '#1F2937',
  textSecondary: '#6B7280',
  textTertiary: '#9CA3AF',
  textInverse: '#FFFFFF',
  textMuted: '#D1D5DB',

  // 边框色 - 更柔和的边框
  border: '#E5E7EB',
  borderLight: '#F3F4F6',
  borderDark: '#D1D5DB',
  borderFocus: '#6366F1',    // 聚焦时的边框色

  // 订阅等级色 - 更有层次感的设计
  subscription: {
    free: '#9CA3AF',         // 中性灰色
    pro: '#6366F1',          // 主品牌色
    max: '#EC4899',          // 活力粉色，体现高端
  },

  // 游戏相关色彩
  game: {
    drawing: '#FF6B6B',      // 绘画模式
    chat: '#4ECDC4',         // 聊天模式
    waiting: '#FFE66D',      // 等待状态
    active: '#10B981',       // 活跃状态
  },
};

export const typography = {
  // 字体族 - 圆润友好的字体选择
  fontFamily: {
    primary: 'System',       // 系统默认字体，确保兼容性
    heading: 'System',       // 标题字体，可以后续替换为自定义字体
    mono: 'Courier',         // 等宽字体，用于代码或特殊场景
  },

  // 字体大小 - 更丰富的尺寸层级
  fontSize: {
    xs: 10,
    sm: 12,
    base: 14,
    lg: 16,
    xl: 18,
    '2xl': 20,
    '3xl': 24,
    '4xl': 28,
    '5xl': 32,
    '6xl': 36,
    '7xl': 42,              // 超大标题
    '8xl': 48,              // 特大标题
  },

  // 字体权重 - 更细致的权重控制
  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    extrabold: '800' as const,
  },

  // 行高 - 更好的可读性
  lineHeight: {
    none: 1.0,
    tight: 1.2,
    snug: 1.3,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },

  // 字母间距
  letterSpacing: {
    tighter: -0.5,
    tight: -0.25,
    normal: 0,
    wide: 0.25,
    wider: 0.5,
    widest: 1.0,
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
  '6xl': 64,
  '7xl': 80,
  '8xl': 96,
};

export const borderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
};

// 更现代的阴影系统 - 更柔和自然
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 3,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 12,
  },
  '2xl': {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.18,
    shadowRadius: 30,
    elevation: 16,
  },
  // 特殊阴影效果
  glow: {
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  colored: {
    shadowColor: '#EC4899',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
};

// 动画配置
export const animations = {
  duration: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};

// 主题对象 - 完整的设计系统
export const theme = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animations,
};

export type Theme = typeof theme;

// 设计原则常量
export const designPrinciples = {
  // 可信 (Trustworthy)
  trustworthy: {
    colors: [colors.primary, colors.primaryDark, colors.gray700],
    message: '使用深色调和专业的配色方案建立信任感',
  },
  // 美观 (Beautiful)
  beautiful: {
    colors: [colors.primarySoft, colors.secondarySoft, colors.tertiarySoft],
    message: '通过柔和的色彩和优雅的设计提升美感',
  },
  // 活力 (Vibrant)
  vibrant: {
    colors: [colors.vibrant.coral, colors.vibrant.mint, colors.vibrant.lemon],
    message: '运用鲜艳的色彩和动态效果增加活力',
  },
};
