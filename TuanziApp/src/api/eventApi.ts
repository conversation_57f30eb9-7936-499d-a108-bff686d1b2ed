import { API_URL } from './client';
import { getToken } from '../auth/storage';
import { EventTemplate, EventStep } from '../types';

export const getEventTemplates = async (): Promise<EventTemplate[]> => {
    const token = await getToken();
    if (!token) {
        throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/events/templates/`, {
        headers: {
            'Authorization': `Bearer ${token}`,
        },
    });
    if (!response.ok) {
        throw new Error('Failed to fetch event templates');
    }
    return response.json();
};

export const createEventTemplate = async (token: string, name: string, description: string): Promise<EventTemplate> => {
    const response = await fetch(`${API_URL}/api/events/templates/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ name, description }),
    });
    if (!response.ok) {
        throw new Error('Failed to create event template');
    }
    return response.json();
};

// --- NEW: Function to get details of a single template ---
export const getTemplateDetails = async (token: string, templateId: number): Promise<EventTemplate> => {
    const response = await fetch(`${API_URL}/api/events/templates/${templateId}/`, {
        headers: {
            'Authorization': `Bearer ${token}`,
        },
    });
    if (!response.ok) {
        throw new Error('Failed to fetch template details');
    }
    return response.json();
};
export const addStepToTemplate = async (token: string, templateId: number, stepData: { name?: string, step_type: string, duration: number }) => {
    const response = await fetch(`${API_URL}/api/events/templates/${templateId}/add-step/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(stepData),
    });
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(JSON.stringify(errorData));
    }
    return response.json();
};

// --- NEW: Function to reorder steps in a template ---
export const reorderSteps = async (token: string, templateId: number, stepIds: number[]): Promise<EventTemplate> => {
    const response = await fetch(`${API_URL}/api/events/templates/${templateId}/reorder-steps/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ step_ids: stepIds }),
    });
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(JSON.stringify(errorData));
    }
    return response.json();
};

// --- NEW: Function to get details of a single step ---
export const getStepDetails = async (token: string, stepId: number): Promise<EventStep> => {
    const response = await fetch(`${API_URL}/api/events/steps/${stepId}/`, {
        headers: {
            'Authorization': `Bearer ${token}`,
        },
    });
    if (!response.ok) {
        throw new Error('Failed to fetch step details');
    }
    return response.json();
};

// --- NEW: Function to update a step ---
export const updateStep = async (token: string, stepId: number, stepData: Partial<EventStep>): Promise<EventStep> => {
    const response = await fetch(`${API_URL}/api/events/steps/${stepId}/`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(stepData),
    });
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(JSON.stringify(errorData));
    }
    return response.json();
};

export const deleteStep = async (token: string, stepId: number): Promise<void> => {
    const response = await fetch(`${API_URL}/api/events/steps/${stepId}/`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`,
        },
    });
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(JSON.stringify(errorData));
    }
};

// --- NEW: Function to add step to room dynamically ---
export const addStepToRoom = async (
    roomCode: string,
    stepData: {
        step_type: string;
        name?: string;
        duration?: number;
        configuration?: Record<string, any>;
    }
): Promise<{ message: string; step: EventStep }> => {
    const token = await getToken();
    if (!token) {
        throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/rooms/${roomCode}/add-step/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(stepData),
    });

    if (!response.ok) {
        let errorMessage = 'Failed to add step to room';
        try {
            const errorData = await response.json();
            errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
            errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
    }

    return response.json();
};

// --- NEW: Function to get room steps for timeline ---
export interface RoomStep {
    id: number;
    order: number;
    name: string;
    step_type: string;
    duration: number;
    configuration: Record<string, any>;
    is_completed: boolean;
    completed_at: string | null;
    is_current: boolean;
}

export interface RoomStepsResponse {
    room_code: string;
    room_status: string;
    current_step_order: number;
    total_steps: number;
    steps: RoomStep[];
    is_host: boolean;
}

export const getRoomSteps = async (roomCode: string): Promise<RoomStepsResponse> => {
    const token = await getToken();
    if (!token) {
        throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/rooms/${roomCode}/steps/`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
    });

    if (!response.ok) {
        let errorMessage = 'Failed to get room steps';
        try {
            const errorData = await response.json();
            errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
            // 如果响应不是JSON格式，使用状态码和状态文本
            errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
    }

    return response.json();
};
