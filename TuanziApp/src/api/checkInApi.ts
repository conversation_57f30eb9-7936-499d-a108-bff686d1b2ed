/**
 * 签到系统API
 * 
 * 提供用户签到相关的API接口
 */

import { API_URL } from './client';
import { getToken } from '../auth/storage';

export interface CheckInStatus {
  has_checked_in_today: boolean;
  consecutive_days: number;
  total_check_ins: number;
  month_check_in_dates: string[];
  today: string;
}

export interface CheckInResult {
  message: string;
  check_in_date: string;
  consecutive_days: number;
  reward_points: number;
  total_check_ins: number;
}

/**
 * 获取用户签到状态
 * @returns 签到状态信息
 */
export const getCheckInStatus = async (): Promise<CheckInStatus> => {
  const token = await getToken();
  if (!token) {
    throw new Error('No authentication token found');
  }

  const response = await fetch(`${API_URL}/api/checkin/`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 执行签到操作
 * @returns 签到结果
 */
export const performCheckIn = async (): Promise<CheckInResult> => {
  const token = await getToken();
  if (!token) {
    throw new Error('No authentication token found');
  }

  const response = await fetch(`${API_URL}/api/checkin/`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

/**
 * 检查指定日期是否已签到
 * @param checkInDates 签到日期数组
 * @param targetDate 目标日期 (YYYY-MM-DD格式)
 * @returns 是否已签到
 */
export const hasCheckedInOnDate = (checkInDates: string[], targetDate: string): boolean => {
  return checkInDates.includes(targetDate);
};

/**
 * 获取本月签到天数
 * @param checkInDates 签到日期数组
 * @returns 本月签到天数
 */
export const getMonthCheckInCount = (checkInDates: string[]): number => {
  return checkInDates.length;
};

/**
 * 格式化连续签到天数显示
 * @param consecutiveDays 连续签到天数
 * @returns 格式化的显示文本
 */
export const formatConsecutiveDays = (consecutiveDays: number): string => {
  if (consecutiveDays === 0) {
    return '还未开始签到';
  } else if (consecutiveDays === 1) {
    return '连续签到 1 天';
  } else {
    return `连续签到 ${consecutiveDays} 天`;
  }
};

/**
 * 格式化总签到天数显示
 * @param totalCheckIns 总签到天数
 * @returns 格式化的显示文本
 */
export const formatTotalCheckIns = (totalCheckIns: number): string => {
  if (totalCheckIns === 0) {
    return '累计签到 0 天';
  } else {
    return `累计签到 ${totalCheckIns} 天`;
  }
};
