import AsyncStorage from '@react-native-async-storage/async-storage';
import { jwtDecode } from 'jwt-decode';

const tokenKey = 'userToken';
const refreshTokenKey = 'refreshToken';
const userKey = 'currentUser';

type DecodedToken = {
    user_id: number;
    username: string;
    subscription_level: 'Free' | 'Pro' | 'Max';
};

/**
 * 存储访问令牌
 * @param token JWT访问令牌
 */
export const storeToken = async (token: string) => {
    try {
        await AsyncStorage.setItem(tokenKey, token);
        await AsyncStorage.setItem('access_token', token); // 为了与TokenRefreshManager兼容

        const decoded: DecodedToken = jwtDecode(token);
        await AsyncStorage.setItem(userKey, decoded.username);
    } catch (error) {
        console.log('Error storing the auth token', error);
    }
};

/**
 * 存储刷新令牌
 * @param refreshToken JWT刷新令牌
 */
export const storeRefreshToken = async (refreshToken: string) => {
    try {
        await AsyncStorage.setItem(refreshTokenKey, refreshToken);
        await AsyncStorage.setItem('refresh_token', refreshToken); // 为了与TokenRefreshManager兼容
    } catch (error) {
        console.log('Error storing the refresh token', error);
    }
};

/**
 * 获取访问令牌
 * @returns 访问令牌或null
 */
export const getToken = () => AsyncStorage.getItem(tokenKey);

/**
 * 获取刷新令牌
 * @returns 刷新令牌或null
 */
export const getRefreshToken = () => AsyncStorage.getItem(refreshTokenKey);

/**
 * 获取用户信息
 * @returns 用户信息对象或null
 */
export const getUser = async () => {
    try {
        const token = await getToken();
        if (!token) return null;

        const decoded: DecodedToken = jwtDecode(token);
        return {
            token,
            username: decoded.username,
            subscription_level: decoded.subscription_level
        };
    } catch (error) {
        console.log('Error getting user from token', error);
        return null;
    }
};

/**
 * 移除所有认证相关的令牌和信息
 */
export const removeToken = async () => {
    try {
        // 确保清除所有可能存储认证信息的键
        await AsyncStorage.multiRemove([
            tokenKey,
            refreshTokenKey,
            userKey,
            'access_token',
            'refresh_token',
            'user_data'
        ]);
    } catch (error) {
        console.log('Error removing the auth token', error);
    }
};
