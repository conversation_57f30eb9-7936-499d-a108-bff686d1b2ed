import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  Animated,
  Dimensions
} from 'react-native';
import { PictionaryCanvas, PathData } from '../PictionaryCanvas';
import { Message } from '../../types';
import { theme } from '../../styles/theme';

const { width } = Dimensions.get('window');

// This component is now fully "dumb". It only renders what it's told to.
interface PictionaryViewProps {
  isDrawer: boolean;
  pictionaryState: {
    drawer: string;
    word: string;
    round_duration?: number;
    current_round?: number;
    max_rounds?: number;
    step_duration?: number;
    total_rounds?: number; // 保持向后兼容
  };
  paths: PathData[];
  messages: Message[];
  onDraw: (pathData: PathData) => void;
  onSendMessage?: (message: string) => void; // 普通聊天消息处理器（可选）
  onSendGuess?: (guess: string) => void; // 专门的猜词处理器（可选）
}

// 庆祝动画组件
const CelebrationEffect: React.FC<{ visible: boolean }> = ({ visible }) => {
  const animations = useRef(
    Array.from({ length: 6 }, () => ({
      scale: new Animated.Value(0),
      translateY: new Animated.Value(0),
      opacity: new Animated.Value(0),
    }))
  ).current;

  useEffect(() => {
    if (visible) {
      const animationPromises = animations.map((anim, index) => {
        return new Promise<void>((resolve) => {
          Animated.sequence([
            Animated.delay(index * 100),
            Animated.parallel([
              Animated.spring(anim.scale, {
                toValue: 1,
                useNativeDriver: true,
              }),
              Animated.timing(anim.opacity, {
                toValue: 1,
                duration: 300,
                useNativeDriver: true,
              }),
              Animated.timing(anim.translateY, {
                toValue: -50,
                duration: 1000,
                useNativeDriver: true,
              }),
            ]),
            Animated.timing(anim.opacity, {
              toValue: 0,
              duration: 500,
              useNativeDriver: true,
            }),
          ]).start(() => {
            anim.scale.setValue(0);
            anim.translateY.setValue(0);
            anim.opacity.setValue(0);
            resolve();
          });
        });
      });
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <View style={styles.celebrationContainer}>
      {animations.map((anim, index) => (
        <Animated.Text
          key={index}
          style={[
            styles.celebrationEmoji,
            {
              left: (width / 6) * index,
              transform: [
                { scale: anim.scale },
                { translateY: anim.translateY },
              ],
              opacity: anim.opacity,
            },
          ]}
        >
          {['🎉', '✨', '🎊', '⭐', '💫', '🌟'][index]}
        </Animated.Text>
      ))}
    </View>
  );
};

export const PictionaryView: React.FC<PictionaryViewProps> = ({
  isDrawer,
  pictionaryState,
  paths,
  messages,
  onDraw,
  onSendMessage,
  onSendGuess,
}) => {
  const [messageInput, setMessageInput] = useState('');
  const [timeLeft, setTimeLeft] = useState(pictionaryState.round_duration || 60);
  const [showCelebration, setShowCelebration] = useState(false);
  const [lastCorrectGuess, setLastCorrectGuess] = useState<string | null>(null);

  // 动画值
  const timerPulse = useRef(new Animated.Value(1)).current;
  const wordReveal = useRef(new Animated.Value(0)).current;

  // 检测正确答案
  useEffect(() => {
    const latestMessage = messages[0];
    if (latestMessage && latestMessage.message.toLowerCase().includes(pictionaryState.word.toLowerCase())) {
      if (latestMessage.sender !== lastCorrectGuess) {
        setShowCelebration(true);
        setLastCorrectGuess(latestMessage.sender);
        setTimeout(() => setShowCelebration(false), 3000);
      }
    }
  }, [messages, pictionaryState.word, lastCorrectGuess]);

  // 计时器效果
  useEffect(() => {
    if (timeLeft <= 0) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // 计时器脉冲动画
  useEffect(() => {
    if (timeLeft <= 10 && timeLeft > 0) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(timerPulse, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(timerPulse, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      timerPulse.setValue(1);
    }
  }, [timeLeft]);

  // 单词显示动画
  useEffect(() => {
    Animated.timing(wordReveal, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, [pictionaryState.word]);

  // Reset timer when game starts
  useEffect(() => {
    setTimeLeft(pictionaryState.round_duration || 60);
  }, [pictionaryState]);

  const handleSendMessage = () => {
    if (messageInput.trim() && !isDrawer) {
      const message = messageInput.trim();
      // 优先使用专门的猜词处理器，否则使用普通消息处理器
      if (onSendGuess) {
        onSendGuess(message);
      } else if (onSendMessage) {
        onSendMessage(message);
      }
      setMessageInput('');
    }
  };

  const getTimerColor = () => {
    if (timeLeft <= 10) return theme.colors.error;
    if (timeLeft <= 30) return theme.colors.warning;
    return theme.colors.success;
  };

  return (
    <View style={styles.gameContainer}>
      {/* 庆祝效果 */}
      <CelebrationEffect visible={showCelebration} />

      {/* 顶部信息面板 - 增强设计 */}
      <View style={styles.topInfoPanel}>
        <View style={styles.drawerInfo}>
          <Text style={styles.drawerLabel}>绘画者</Text>
          <Text style={styles.drawerName}>{pictionaryState.drawer}</Text>
        </View>

        {/* 局数信息 */}
        {pictionaryState.current_round && (
          <View style={styles.roundInfo}>
            <Text style={styles.roundText}>
              第 {pictionaryState.current_round} 局
              {pictionaryState.max_rounds && pictionaryState.max_rounds < 999 &&
                `/${pictionaryState.max_rounds}`
              }
            </Text>
          </View>
        )}

        <Animated.View
          style={[
            styles.timerContainer,
            { transform: [{ scale: timerPulse }] }
          ]}
        >
          <Text style={[styles.timerText, { color: getTimerColor() }]}>
            {timeLeft}s
          </Text>
          <View style={[styles.timerProgress, { backgroundColor: getTimerColor() }]} />
        </Animated.View>
      </View>

      {/* 单词面板 - 动画效果 */}
      <Animated.View
        style={[
          styles.wordPanel,
          {
            opacity: wordReveal,
            transform: [{ scale: wordReveal }],
          }
        ]}
      >
        <Text style={styles.wordText}>{pictionaryState.word}</Text>
        {isDrawer ? (
          <Text style={styles.roleText}>🎨 你是绘画者</Text>
        ) : (
          <Text style={styles.roleText}>🤔 猜猜这是什么</Text>
        )}
      </Animated.View>
      {/* 主要游戏区域 */}
      <View style={styles.mainArea}>
        {/* 画布容器 - 增强边框 */}
        <View style={[
          styles.canvasContainer,
          isDrawer && styles.canvasContainerActive
        ]}>
          <PictionaryCanvas
            isDrawer={isDrawer}
            onDraw={onDraw}
            paths={paths}
          />
          {isDrawer && (
            <View style={styles.canvasOverlay}>
              <Text style={styles.canvasHint}>在这里绘画 ✏️</Text>
            </View>
          )}
        </View>

        {/* 聊天容器 - 现代化设计 */}
        <View style={styles.chatContainer}>
          <View style={styles.chatHeader}>
            <Text style={styles.chatTitle}>💭 猜测区</Text>
            <Text style={styles.messageCount}>{messages.length} 条消息</Text>
          </View>

          <FlatList
            style={styles.chatList}
            data={messages}
            renderItem={({ item }) => (
              <View style={[
                styles.messageBubble,
                item.message.toLowerCase().includes(pictionaryState.word.toLowerCase()) &&
                styles.correctGuessBubble
              ]}>
                <Text style={styles.messageSender}>{item.sender}</Text>
                <Text style={styles.messageText}>{item.message}</Text>
                {item.message.toLowerCase().includes(pictionaryState.word.toLowerCase()) && (
                  <Text style={styles.correctIcon}>🎉</Text>
                )}
              </View>
            )}
            keyExtractor={(_, index) => index.toString()}
            inverted
            showsVerticalScrollIndicator={false}
          />

          <View style={styles.inputArea}>
            <TextInput
              style={[
                styles.chatInput,
                !isDrawer && styles.chatInputActive
              ]}
              placeholder={isDrawer ? "绘画中，无法发送消息" : "输入你的猜测..."}
              placeholderTextColor={theme.colors.textTertiary}
              value={messageInput}
              onChangeText={setMessageInput}
              editable={!isDrawer}
              multiline
              maxLength={100}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                (!isDrawer && messageInput.trim()) ? styles.sendButtonActive : styles.sendButtonInactive
              ]}
              onPress={handleSendMessage}
              disabled={isDrawer || !messageInput.trim()}
            >
              <Text style={[
                styles.sendButtonText,
                (!isDrawer && messageInput.trim()) ? styles.sendButtonTextActive : styles.sendButtonTextInactive
              ]}>
                猜测
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  gameContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
  },

  // 庆祝效果
  celebrationContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 200,
    zIndex: 1000,
    pointerEvents: 'none',
  },
  celebrationEmoji: {
    position: 'absolute',
    fontSize: 30,
    top: 50,
  },

  // 顶部信息面板
  topInfoPanel: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.xl,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.md,
  },
  drawerInfo: {
    alignItems: 'flex-start',
  },
  drawerLabel: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  drawerName: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.primary,
  },
  roundInfo: {
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  roundText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.textPrimary,
  },
  timerContainer: {
    alignItems: 'center',
    minWidth: 80,
  },
  timerText: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing.xs,
  },
  timerProgress: {
    height: 4,
    width: 60,
    borderRadius: 2,
  },

  // 单词面板
  wordPanel: {
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: theme.spacing['2xl'],
    borderRadius: theme.borderRadius.xl,
    marginBottom: theme.spacing.lg,
    ...theme.shadows.lg,
  },
  wordText: {
    fontSize: theme.typography.fontSize['4xl'],
    fontWeight: theme.typography.fontWeight.bold,
    letterSpacing: theme.typography.letterSpacing.wider,
    color: theme.colors.textPrimary,
    textAlign: 'center',
  },
  roleText: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
    textAlign: 'center',
  },

  // 主要区域
  mainArea: {
    flex: 1,
    flexDirection: 'row',
    gap: theme.spacing.md,
  },

  // 画布容器
  canvasContainer: {
    flex: 2,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: theme.colors.border,
    position: 'relative',
  },
  canvasContainerActive: {
    borderColor: theme.colors.primary,
    ...theme.shadows.glow,
  },
  canvasOverlay: {
    position: 'absolute',
    top: theme.spacing.md,
    left: theme.spacing.md,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
  },
  canvasHint: {
    color: theme.colors.white,
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.medium,
  },

  // 聊天容器
  chatContainer: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    ...theme.shadows.md,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.gray50,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  chatTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  messageCount: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  chatList: {
    flex: 1,
    padding: theme.spacing.md,
  },
  messageBubble: {
    backgroundColor: theme.colors.background,
    padding: theme.spacing.md,
    marginVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: theme.colors.border,
    position: 'relative',
  },
  correctGuessBubble: {
    backgroundColor: theme.colors.successSoft,
    borderColor: theme.colors.success,
  },
  messageSender: {
    fontWeight: theme.typography.fontWeight.semibold,
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  messageText: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
  },
  correctIcon: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    fontSize: theme.typography.fontSize.base,
  },

  // 输入区域
  inputArea: {
    flexDirection: 'row',
    padding: theme.spacing.md,
    gap: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    backgroundColor: theme.colors.gray50,
  },
  chatInput: {
    flex: 1,
    minHeight: 36,
    maxHeight: 72,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    backgroundColor: theme.colors.gray200,
  },
  chatInputActive: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.surface,
    color: theme.colors.textPrimary,
  },
  sendButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    minWidth: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  sendButtonInactive: {
    backgroundColor: theme.colors.gray300,
  },
  sendButtonText: {
    fontSize: theme.typography.fontSize.sm,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  sendButtonTextActive: {
    color: theme.colors.white,
  },
  sendButtonTextInactive: {
    color: theme.colors.gray500,
  },
});
