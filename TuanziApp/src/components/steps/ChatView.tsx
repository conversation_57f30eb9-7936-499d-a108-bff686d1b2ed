import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Animated,
  TouchableOpacity
} from 'react-native';
import { useAuth } from '../../auth/AuthContext';
import { Message } from '../../types';
import { Button } from '../ui';
import { theme } from '../../styles/theme';

// Props that this component receives from RoomScreen
interface ChatViewProps {
  messages: Message[];
  onSendMessage: (message: string) => void;
  roomHost: string;
  // onNextStep is no longer needed here as it's handled globally
}

// 消息项组件 - 带动画效果
const MessageItem: React.FC<{ item: Message; isOwn: boolean; index: number }> = ({
  item,
  isOwn,
  index
}) => {
  const slideAnim = useRef(new Animated.Value(50)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        delay: index * 50, // 错开动画时间
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 300,
        delay: index * 50,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <Animated.View
      style={[
        isOwn ? styles.myMessageBubble : styles.theirMessageBubble,
        {
          transform: [{ translateY: slideAnim }],
          opacity: opacityAnim,
        },
      ]}
    >
      <Text style={styles.messageSender}>{item.sender}</Text>
      <Text style={styles.messageText}>{item.message}</Text>
    </Animated.View>
  );
};

export const ChatView: React.FC<ChatViewProps> = ({ messages, onSendMessage }) => {
  const { user } = useAuth();
  const [messageInput, setMessageInput] = useState<string>('');
  const [isTyping, setIsTyping] = useState(false);
  const inputRef = useRef<TextInput>(null);

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      onSendMessage(messageInput.trim());
      setMessageInput('');
      setIsTyping(false);

      // 添加发送成功的触觉反馈
      // HapticFeedback.impactAsync(HapticFeedback.ImpactFeedbackStyle.Light);
    }
  };

  const handleInputChange = (text: string) => {
    setMessageInput(text);
    setIsTyping(text.length > 0);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      {/* 头部 - 更现代的设计 */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.title}>💬 自由讨论</Text>
          <View style={styles.onlineIndicator}>
            <View style={styles.onlineDot} />
            <Text style={styles.onlineText}>在线</Text>
          </View>
        </View>
      </View>

      {/* 消息列表 - 使用自定义消息组件 */}
      <FlatList
        style={styles.chatList}
        data={messages}
        renderItem={({ item, index }) => (
          <MessageItem
            item={item}
            isOwn={item.sender === user?.username}
            index={index}
          />
        )}
        keyExtractor={(_, index) => index.toString()}
        inverted
        showsVerticalScrollIndicator={false}
      />

      {/* 输入区域 - 增强的设计 */}
      <View style={styles.inputArea}>
        <View style={styles.inputContainer}>
          <TextInput
            ref={inputRef}
            style={[
              styles.chatInput,
              isTyping && styles.chatInputActive
            ]}
            placeholder="输入聊天消息..."
            placeholderTextColor={theme.colors.textTertiary}
            value={messageInput}
            onChangeText={handleInputChange}
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              messageInput.trim() ? styles.sendButtonActive : styles.sendButtonInactive
            ]}
            onPress={handleSendMessage}
            disabled={!messageInput.trim()}
          >
            <Text style={[
              styles.sendButtonText,
              messageInput.trim() ? styles.sendButtonTextActive : styles.sendButtonTextInactive
            ]}>
              发送
            </Text>
          </TouchableOpacity>
        </View>

        {/* 字符计数 */}
        {messageInput.length > 400 && (
          <Text style={styles.characterCount}>
            {messageInput.length}/500
          </Text>
        )}
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  // 头部样式
  header: {
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    ...theme.shadows.sm,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  title: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
  },
  onlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  onlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.success,
  },
  onlineText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.success,
    fontWeight: theme.typography.fontWeight.medium,
  },

  // 聊天列表
  chatList: {
    flex: 1,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },

  // 消息气泡样式
  myMessageBubble: {
    backgroundColor: theme.colors.primary,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.xl,
    marginVertical: theme.spacing.xs,
    maxWidth: '80%',
    alignSelf: 'flex-end',
    marginRight: theme.spacing.md,
    ...theme.shadows.sm,
  },
  theirMessageBubble: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.xl,
    marginVertical: theme.spacing.xs,
    maxWidth: '80%',
    alignSelf: 'flex-start',
    marginLeft: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  messageSender: {
    fontWeight: theme.typography.fontWeight.semibold,
    marginBottom: theme.spacing.xs,
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.white, // 对于自己的消息
  },
  messageText: {
    fontSize: theme.typography.fontSize.base,
    lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.base,
    color: theme.colors.white, // 对于自己的消息
  },

  // 输入区域
  inputArea: {
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    padding: theme.spacing.lg,
    ...theme.shadows.sm,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: theme.spacing.md,
  },
  chatInput: {
    flex: 1,
    minHeight: 44,
    maxHeight: 100,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.xl,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    backgroundColor: theme.colors.background,
  },
  chatInputActive: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.surface,
  },
  sendButton: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.xl,
    minWidth: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  sendButtonInactive: {
    backgroundColor: theme.colors.gray300,
  },
  sendButtonText: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  sendButtonTextActive: {
    color: theme.colors.white,
  },
  sendButtonTextInactive: {
    color: theme.colors.gray500,
  },
  characterCount: {
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.textTertiary,
    textAlign: 'right',
    marginTop: theme.spacing.xs,
  },
});
