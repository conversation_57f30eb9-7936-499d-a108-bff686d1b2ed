import React, { useState } from 'react';
import {
  View,
  Text,
  Button,
  StyleSheet,
  ActivityIndicator,
  ScrollView,
  TouchableOpacity,
  TextInput,
  FlatList,
  Animated,
} from 'react-native';
import { Message } from '../../types';
import { theme } from '../../styles/theme';

interface LobbyViewProps {
  isHost?: boolean;
  onNextStep?: () => void;
  isFinished?: boolean;
  isConnected?: boolean;
  // 新增属性
  messages?: Message[];
  onSendMessage?: (message: string) => void;
  activityHistory?: ActivityHistoryItem[];
  participants?: Participant[];
  onAddStep?: () => void;
  // 房间条件状态
  isReadyForNextStep?: boolean;
  conditionMessage?: string;
  nextStepInfo?: any;
}

interface ActivityHistoryItem {
  id: string;
  type: 'step_completed' | 'game_result' | 'user_joined' | 'user_left';
  title: string;
  description: string;
  timestamp: string;
  data?: any;
}

interface Participant {
  username: string;
  role: 'host' | 'admin' | 'participant';
  isActive: boolean;
  joinedAt: string;
}

export const LobbyView: React.FC<LobbyViewProps> = ({
  isHost,
  onNextStep,
  isFinished,
  isConnected,
  messages = [],
  onSendMessage,
  activityHistory = [],
  participants = [],
  onAddStep,
  isReadyForNextStep = false,
  conditionMessage = '',
  nextStepInfo = null
}) => {
  const [currentMessage, setCurrentMessage] = useState('');
  const [activeTab, setActiveTab] = useState<'chat' | 'history' | 'participants'>('chat');

  const handleSendMessage = () => {
    if (currentMessage.trim() && onSendMessage) {
      onSendMessage(currentMessage.trim());
      setCurrentMessage('');
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <View style={styles.chatContainer}>
            <FlatList
              data={messages}
              keyExtractor={(item, index) => `${item.id || index}`}
              renderItem={({ item }) => (
                <View style={styles.messageItem}>
                  <Text style={styles.messageUser}>{item.user}:</Text>
                  <Text style={styles.messageText}>{item.message}</Text>
                </View>
              )}
              style={styles.messagesList}
              inverted
            />
            {onSendMessage && (
              <View style={styles.messageInputContainer}>
                <TextInput
                  style={styles.messageInput}
                  value={currentMessage}
                  onChangeText={setCurrentMessage}
                  placeholder="输入消息..."
                  multiline
                />
                <TouchableOpacity
                  style={styles.sendButton}
                  onPress={handleSendMessage}
                  disabled={!currentMessage.trim()}
                >
                  <Text style={styles.sendButtonText}>发送</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        );

      case 'history':
        return (
          <View style={styles.historyContainer}>
            <Text style={styles.sectionTitle}>活动历史</Text>
            <FlatList
              data={activityHistory}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <View style={styles.historyItem}>
                  <Text style={styles.historyTitle}>{item.title}</Text>
                  <Text style={styles.historyDescription}>{item.description}</Text>
                  <Text style={styles.historyTime}>{item.timestamp}</Text>
                </View>
              )}
              ListEmptyComponent={
                <Text style={styles.emptyText}>暂无活动历史</Text>
              }
            />
          </View>
        );

      case 'participants':
        return (
          <View style={styles.participantsContainer}>
            <Text style={styles.sectionTitle}>参与者 ({participants.length})</Text>
            <FlatList
              data={participants}
              keyExtractor={(item) => item.username}
              renderItem={({ item }) => (
                <View style={styles.participantItem}>
                  <Text style={styles.participantName}>
                    {item.username}
                    {item.role === 'host' && ' 👑'}
                    {item.role === 'admin' && ' ⭐'}
                  </Text>
                  <Text style={[
                    styles.participantStatus,
                    { color: item.isActive ? '#4CAF50' : '#999' }
                  ]}>
                    {item.isActive ? '在线' : '离线'}
                  </Text>
                </View>
              )}
              ListEmptyComponent={
                <Text style={styles.emptyText}>暂无参与者</Text>
              }
            />
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* 状态栏 */}
      <View style={styles.statusBar}>
        {isFinished ? (
          <Text style={styles.statusText}>🎉 所有环节已结束！</Text>
        ) : (
          <>
            <Text style={styles.statusText}>
              {isHost ? (
                conditionMessage === '没有更多环节' ?
                  '🎉 所有环节已完成！您可以添加新环节或结束活动。' :
                  isReadyForNextStep ?
                    `🎮 准备就绪！${nextStepInfo ? `下一环节：${nextStepInfo.name}` : ''}` :
                    `⏳ ${conditionMessage || '检查环节条件中...'}`
              ) : (
                conditionMessage === '没有更多环节' ?
                  '🎉 所有环节已完成！等待房主操作...' :
                  isReadyForNextStep ?
                    '✅ 条件满足，等待房主开始下一环节...' :
                    `⏳ ${conditionMessage || '等待满足开始条件...'}`
              )}
            </Text>
            {!isConnected && <ActivityIndicator style={styles.spinner} />}
          </>
        )}
      </View>

      {/* 房主操作区 */}
      {isHost && !isFinished && (
        <View style={styles.hostActions}>
          {/* 当没有更多环节时，隐藏开始下一环节按钮 */}
          {conditionMessage !== '没有更多环节' && (
            <TouchableOpacity
              style={[
                styles.actionButton,
                isReadyForNextStep ? styles.primaryButton : styles.disabledButton
              ]}
              onPress={onNextStep}
              disabled={!isConnected || !isReadyForNextStep}
            >
              <Text style={[
                styles.actionButtonText,
                !isReadyForNextStep && styles.disabledButtonText
              ]}>
                {!isConnected ? '正在连接...' :
                 isReadyForNextStep ?
                   `📋 开始${nextStepInfo ? nextStepInfo.name : '下一环节'}` :
                   '⏳ 等待条件满足'}
              </Text>
            </TouchableOpacity>
          )}

          {onAddStep && (
            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryButton]}
              onPress={onAddStep}
              disabled={!isConnected}
            >
              <Text style={styles.actionButtonText}>➕ 添加新环节</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* 标签页导航 */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'chat' && styles.activeTab]}
          onPress={() => setActiveTab('chat')}
        >
          <Text style={[styles.tabText, activeTab === 'chat' && styles.activeTabText]}>
            💬 聊天
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'history' && styles.activeTab]}
          onPress={() => setActiveTab('history')}
        >
          <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
            📊 历史
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'participants' && styles.activeTab]}
          onPress={() => setActiveTab('participants')}
        >
          <Text style={[styles.tabText, activeTab === 'participants' && styles.activeTabText]}>
            👥 成员
          </Text>
        </TouchableOpacity>
      </View>

      {/* 内容区域 */}
      <View style={styles.contentContainer}>
        {renderTabContent()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  statusBar: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    ...theme.shadows.sm,
  },
  statusText: {
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.textPrimary,
    textAlign: 'center',
    fontWeight: theme.typography.fontWeight.medium,
  },
  spinner: {
    marginTop: 8,
  },
  hostActions: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.lg,
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    ...theme.shadows.sm,
  },
  actionButton: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    minWidth: 120,
    alignItems: 'center',
    ...theme.shadows.md,
  },
  primaryButton: {
    backgroundColor: theme.colors.primary,
  },
  secondaryButton: {
    backgroundColor: theme.colors.success,
  },
  disabledButton: {
    backgroundColor: theme.colors.gray300,
    ...theme.shadows.none,
  },
  actionButtonText: {
    color: theme.colors.white,
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  disabledButtonText: {
    color: theme.colors.textTertiary,
  },
  tabContainer: {
    backgroundColor: theme.colors.surface,
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    ...theme.shadows.sm,
  },
  tab: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: theme.colors.primary,
  },
  tabText: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.fontWeight.medium,
  },
  activeTabText: {
    color: theme.colors.primary,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  contentContainer: {
    flex: 1,
  },
  // 聊天相关样式
  chatContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
  },
  messageItem: {
    marginVertical: theme.spacing.xs,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary,
  },
  messageUser: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.primary,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  messageText: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    marginTop: theme.spacing.xs,
    lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.base,
  },
  messageInputContainer: {
    flexDirection: 'row',
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    alignItems: 'flex-end',
    ...theme.shadows.sm,
  },
  messageInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius['2xl'],
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    marginRight: theme.spacing.md,
    maxHeight: 80,
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    backgroundColor: theme.colors.background,
  },
  sendButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius['2xl'],
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 60,
    ...theme.shadows.md,
  },
  sendButtonText: {
    color: theme.colors.white,
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  // 历史记录样式
  historyContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.lg,
  },
  historyItem: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.sm,
    ...theme.shadows.sm,
  },
  historyTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  historyDescription: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  historyTime: {
    fontSize: theme.typography.fontSize.xs,
    color: theme.colors.textTertiary,
    marginTop: theme.spacing.xs,
  },
  // 参与者样式
  participantsContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: theme.spacing.lg,
  },
  participantItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.borderLight,
  },
  participantName: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    fontWeight: theme.typography.fontWeight.medium,
  },
  participantStatus: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  emptyText: {
    textAlign: 'center',
    color: theme.colors.textTertiary,
    fontSize: theme.typography.fontSize.base,
    marginTop: theme.spacing.xl,
  },
});
