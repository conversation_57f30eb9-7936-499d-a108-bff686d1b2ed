/**
 * 订阅状态卡片组件
 * 
 * 功能：
 * - 显示当前订阅状态
 * - 突出显示当前计划信息
 * - 美观的状态展示
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card, Badge } from '../ui';
import { theme } from '../../styles/theme';

type SubscriptionLevel = 'Free' | 'Pro' | 'Max';

interface SubscriptionPlan {
  level: SubscriptionLevel;
  name: string;
  price: number;
  priceText: string;
  features: string[];
  roomLimit: string;
  timeLimit: string;
  color: string;
}

interface SubscriptionStatusCardProps {
  currentPlan: SubscriptionPlan;
}

/**
 * 订阅状态卡片组件 - 重新设计版本
 *
 * 新特性：
 * - 更现代的视觉设计
 * - 增强的信任感展示
 * - 更清晰的信息层次
 */
export const SubscriptionStatusCard: React.FC<SubscriptionStatusCardProps> = ({
  currentPlan,
}) => {
  const getBadgeVariant = (level: SubscriptionLevel) => {
    switch (level) {
      case 'Free':
        return 'neutral';
      case 'Pro':
        return 'primary';
      case 'Max':
        return 'vibrant';
      default:
        return 'neutral';
    }
  };

  return (
    <Card
      variant="elevated"
      style={[styles.statusCard, { borderTopColor: currentPlan.color }]}
      glowEffect={currentPlan.level !== 'Free'}
    >
      <View style={styles.statusHeader}>
        <Text style={styles.statusTitle}>当前订阅计划</Text>
        <Badge
          text={currentPlan.level}
          variant={getBadgeVariant(currentPlan.level)}
          size="medium"
        />
      </View>

      <View style={styles.statusContent}>
        <Text style={[styles.statusLevel, { color: currentPlan.color }]}>
          {currentPlan.name}
        </Text>
        <Text style={styles.statusPrice}>{currentPlan.priceText}</Text>

        {currentPlan.level !== 'Free' && (
          <View style={styles.benefitsContainer}>
            <Text style={styles.benefitsTitle}>您正在享受：</Text>
            <Text style={styles.benefitsText}>• 无限制房间创建</Text>
            <Text style={styles.benefitsText}>• 高级功能访问</Text>
            <Text style={styles.benefitsText}>• 优先客服支持</Text>
          </View>
        )}
      </View>
      
      {/* 快速信息 */}
      <View style={styles.quickInfoContainer}>
        <View style={styles.quickInfoItem}>
          <Text style={styles.quickInfoLabel}>房间限制</Text>
          <Text style={styles.quickInfoValue}>{currentPlan.roomLimit}</Text>
        </View>
        <View style={styles.quickInfoDivider} />
        <View style={styles.quickInfoItem}>
          <Text style={styles.quickInfoLabel}>时长限制</Text>
          <Text style={styles.quickInfoValue}>{currentPlan.timeLimit}</Text>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  statusCard: {
    marginBottom: theme.spacing['2xl'],
    borderTopWidth: 4,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  statusTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  statusContent: {
    alignItems: 'center',
  },
  statusLevel: {
    fontSize: theme.typography.fontSize['4xl'],
    fontWeight: theme.typography.fontWeight.bold,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  statusPrice: {
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.lg,
    textAlign: 'center',
  },
  benefitsContainer: {
    width: '100%',
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  benefitsTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.sm,
  },
  benefitsText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
    lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.sm,
  },
  quickInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    width: '100%',
  },
  quickInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  quickInfoLabel: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  quickInfoValue: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  quickInfoDivider: {
    width: 1,
    height: 30,
    backgroundColor: theme.colors.border,
    marginHorizontal: theme.spacing.lg,
  },
});
