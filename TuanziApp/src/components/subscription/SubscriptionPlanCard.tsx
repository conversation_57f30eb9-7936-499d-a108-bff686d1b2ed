/**
 * 订阅计划卡片组件
 * 
 * 功能：
 * - 显示订阅计划详细信息
 * - 支持当前计划标识
 * - 升级/降级按钮
 * - 调试模式支持
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Card, Button, Badge } from '../ui';
import { theme } from '../../styles/theme';

type SubscriptionLevel = 'Free' | 'Pro' | 'Max';

interface SubscriptionPlan {
  level: SubscriptionLevel;
  name: string;
  price: number;
  priceText: string;
  features: string[];
  roomLimit: string;
  timeLimit: string;
  color: string;
}

interface SubscriptionPlanCardProps {
  plan: SubscriptionPlan;
  isCurrentPlan: boolean;
  isUpgrade: boolean;
  isDebugMode: boolean;
  onUpgrade: (level: SubscriptionLevel) => void;
  onDebugChange: (level: SubscriptionLevel) => void;
}

/**
 * 订阅计划卡片组件
 */
export const SubscriptionPlanCard: React.FC<SubscriptionPlanCardProps> = ({
  plan,
  isCurrentPlan,
  isUpgrade,
  isDebugMode,
  onUpgrade,
  onDebugChange,
}) => {
  return (
    <Card
      variant={isCurrentPlan ? "vibrant" : "elevated"}
      style={[
        styles.planCard,
        isCurrentPlan && styles.currentPlanCard
      ]}
      borderWidth={isCurrentPlan ? 2 : 0}
      borderColor={isCurrentPlan ? theme.colors.success : undefined}
      padding={0}
      glowEffect={isCurrentPlan}
    >
      {/* 计划头部 */}
      <View style={[styles.planHeader, { backgroundColor: plan.color }]}>
        <View style={styles.planHeaderContent}>
          <Text style={styles.planName}>{plan.name}</Text>
          {isCurrentPlan && (
            <Badge
              text="当前计划"
              variant="success"
              size="small"
              backgroundColor="rgba(255, 255, 255, 0.9)"
              textColor={theme.colors.success}
            />
          )}
        </View>
        <Text style={styles.planPrice}>{plan.priceText}</Text>
        {plan.level !== 'Free' && (
          <Text style={styles.planPeriod}>每月</Text>
        )}
      </View>

      {/* 计划内容 */}
      <View style={styles.planContent}>
        {/* 限制信息 - 更现代的展示 */}
        <View style={styles.limitsSection}>
          <View style={styles.limitItem}>
            <Text style={styles.limitLabel}>房间数量</Text>
            <Text style={styles.limitValue}>{plan.roomLimit}</Text>
          </View>
          <View style={styles.limitItem}>
            <Text style={styles.limitLabel}>时长限制</Text>
            <Text style={styles.limitValue}>{plan.timeLimit}</Text>
          </View>
        </View>

        {/* 功能特性 - 更清晰的列表 */}
        <View style={styles.featuresSection}>
          <Text style={styles.featuresTitle}>包含功能</Text>
          {plan.features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Text style={styles.featureIcon}>✓</Text>
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </View>

        {/* 操作按钮 */}
        <View style={styles.actionSection}>
          {!isCurrentPlan && (
            <Button
              title={isUpgrade ? '升级' : '降级'}
              onPress={() => onUpgrade(plan.level)}
              variant={isUpgrade ? 'success' : 'warning'}
              style={styles.actionButton}
            />
          )}
          
          {isDebugMode && (
            <Button
              title="调试切换"
              onPress={() => onDebugChange(plan.level)}
              variant="secondary"
              size="small"
              style={styles.debugButton}
            />
          )}
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  planCard: {
    marginBottom: theme.spacing.lg,
    overflow: 'hidden',
  },
  currentPlanCard: {
    transform: [{ scale: 1.02 }],
  },

  // 头部样式
  planHeader: {
    padding: theme.spacing['2xl'],
    alignItems: 'center',
    position: 'relative',
  },
  planHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: theme.spacing.md,
  },
  planName: {
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.white,
  },
  planPrice: {
    fontSize: theme.typography.fontSize['5xl'],
    fontWeight: theme.typography.fontWeight.extrabold,
    color: theme.colors.white,
    textAlign: 'center',
  },
  planPeriod: {
    fontSize: theme.typography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },

  // 内容样式
  planContent: {
    padding: theme.spacing['2xl'],
    backgroundColor: theme.colors.surface,
  },

  // 限制信息样式
  limitsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: theme.spacing['2xl'],
    paddingVertical: theme.spacing.lg,
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.lg,
  },
  limitItem: {
    alignItems: 'center',
  },
  limitLabel: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  limitValue: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
  },

  // 功能特性样式
  featuresSection: {
    marginBottom: theme.spacing.xl,
  },
  featuresTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  featureIcon: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.success,
    marginRight: theme.spacing.md,
    fontWeight: theme.typography.fontWeight.bold,
  },
  featureText: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
    lineHeight: theme.typography.lineHeight.relaxed * theme.typography.fontSize.base,
    flex: 1,
  },

  // 操作区域
  actionSection: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  actionButton: {
    flex: 1,
  },
  debugButton: {
    flex: 1,
  },
});
