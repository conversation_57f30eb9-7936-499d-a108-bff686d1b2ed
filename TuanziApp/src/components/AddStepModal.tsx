import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
  Animated,
  Dimensions,
} from 'react-native';
import { theme } from '../styles/theme';

interface AddStepModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAddStep: (stepData: StepData) => Promise<void>;
  roomCode: string;
}

interface StepData {
  step_type: string;
  name?: string;
  duration?: number;
  configuration?: Record<string, any>;
}

interface StepType {
  id: string;
  name: string;
  description: string;
  icon: string;
  isPremium: boolean;
  defaultDuration: number;
}

const STEP_TYPES: StepType[] = [
  {
    id: 'GAME_PICTIONARY',
    name: '你画我猜',
    description: '经典绘图猜词游戏',
    icon: '🎨',
    isPremium: false,
    defaultDuration: 300,
  },
  {
    id: 'FREE_CHAT',
    name: '自由讨论',
    description: '开放式聊天交流',
    icon: '💬',
    isPremium: false,
    defaultDuration: 600,
  },
  {
    id: 'PAUSE',
    name: '暂停环节',
    description: '休息时间',
    icon: '⏸️',
    isPremium: true,
    defaultDuration: 300,
  },
  {
    id: 'SPEECH',
    name: '发言环节',
    description: '轮流发言分享',
    icon: '🎤',
    isPremium: true,
    defaultDuration: 900,
  },
  {
    id: 'POLL',
    name: '投票环节',
    description: '集体投票决策',
    icon: '🗳️',
    isPremium: true,
    defaultDuration: 300,
  },
  {
    id: 'QNA',
    name: '问答环节',
    description: '互动问答',
    icon: '❓',
    isPremium: true,
    defaultDuration: 600,
  },
];

export const AddStepModal: React.FC<AddStepModalProps> = ({
  isVisible,
  onClose,
  onAddStep,
  roomCode,
}) => {
  const [selectedStepType, setSelectedStepType] = useState<string>('');
  const [stepName, setStepName] = useState<string>('');
  const [duration, setDuration] = useState<string>('300');
  const [roundDuration, setRoundDuration] = useState<string>('60');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleStepTypeSelect = (stepType: StepType) => {
    setSelectedStepType(stepType.id);
    setStepName(stepType.name);
    setDuration(stepType.defaultDuration.toString());
  };

  const handleSubmit = async () => {
    if (!selectedStepType) {
      Alert.alert('错误', '请选择环节类型');
      return;
    }

    const durationNum = parseInt(duration);
    if (isNaN(durationNum) || durationNum < 30 || durationNum > 3600) {
      Alert.alert('错误', '持续时间必须在30秒到3600秒之间');
      return;
    }

    setIsLoading(true);
    try {
      const configuration: Record<string, any> = {};

      // 如果是你画我猜，添加单局时长配置
      if (selectedStepType === 'GAME_PICTIONARY') {
        const roundDurationNum = parseInt(roundDuration);
        if (!isNaN(roundDurationNum) && roundDurationNum >= 30 && roundDurationNum <= 300) {
          configuration.round_duration = roundDurationNum;
        }
      }

      await onAddStep({
        step_type: selectedStepType,
        name: stepName.trim() || undefined,
        duration: durationNum,
        configuration,
      });
      
      // 重置表单
      setSelectedStepType('');
      setStepName('');
      setDuration('300');
      setRoundDuration('60');
      onClose();
    } catch (error) {
      console.error('添加环节失败:', error);
      Alert.alert('错误', '添加环节失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setSelectedStepType('');
      setStepName('');
      setDuration('300');
      setRoundDuration('60');
      onClose();
    }
  };

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <View style={styles.header}>
            <Text style={styles.title}>添加新环节</Text>
            <TouchableOpacity 
              onPress={handleClose} 
              style={styles.closeButton}
              disabled={isLoading}
            >
              <Text style={styles.closeButtonText}>×</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            {/* 环节类型选择 */}
            <Text style={styles.sectionTitle}>选择环节类型</Text>
            <View style={styles.stepTypesContainer}>
              {STEP_TYPES.map((stepType) => (
                <TouchableOpacity
                  key={stepType.id}
                  style={[
                    styles.stepTypeCard,
                    selectedStepType === stepType.id && styles.selectedStepType,
                  ]}
                  onPress={() => handleStepTypeSelect(stepType)}
                  disabled={isLoading}
                >
                  <Text style={styles.stepTypeIcon}>{stepType.icon}</Text>
                  <Text style={styles.stepTypeName}>{stepType.name}</Text>
                  <Text style={styles.stepTypeDescription}>
                    {stepType.description}
                  </Text>
                  {stepType.isPremium && (
                    <Text style={styles.premiumBadge}>Pro</Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>

            {/* 环节配置 */}
            {selectedStepType && (
              <View style={styles.configSection}>
                <Text style={styles.sectionTitle}>环节配置</Text>
                
                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>环节名称（可选）</Text>
                  <TextInput
                    style={styles.textInput}
                    value={stepName}
                    onChangeText={setStepName}
                    placeholder="自定义环节名称"
                    editable={!isLoading}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.inputLabel}>环节总时长（秒）</Text>
                  <TextInput
                    style={styles.textInput}
                    value={duration}
                    onChangeText={setDuration}
                    placeholder="300"
                    keyboardType="numeric"
                    editable={!isLoading}
                  />
                  <Text style={styles.inputHint}>
                    建议：30-3600秒（0.5分钟-1小时）
                  </Text>
                </View>

                {/* 你画我猜特殊配置 */}
                {selectedStepType === 'GAME_PICTIONARY' && (
                  <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>单局时长（秒）</Text>
                    <TextInput
                      style={styles.textInput}
                      value={roundDuration}
                      onChangeText={setRoundDuration}
                      placeholder="60"
                      keyboardType="numeric"
                      editable={!isLoading}
                    />
                    <Text style={styles.inputHint}>
                      每局你画我猜的时长：30-300秒（0.5-5分钟）
                    </Text>
                  </View>
                )}
              </View>
            )}
          </ScrollView>

          <View style={styles.footer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={handleClose}
              disabled={isLoading}
            >
              <Text style={styles.cancelButtonText}>取消</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.button,
                styles.submitButton,
                (!selectedStepType || isLoading) && styles.disabledButton,
              ]}
              onPress={handleSubmit}
              disabled={!selectedStepType || isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.submitButtonText}>添加环节</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius['2xl'],
    width: '90%',
    maxHeight: '85%',
    overflow: 'hidden',
    ...theme.shadows.xl,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.gray50,
  },
  title: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.fontWeight.bold,
  },
  content: {
    padding: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.md,
  },
  stepTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.xl,
  },
  stepTypeCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
    alignItems: 'center',
  },
  selectedStepType: {
    borderColor: '#2196F3',
    backgroundColor: '#e3f2fd',
  },
  stepTypeIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  stepTypeName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    marginBottom: 2,
  },
  stepTypeDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  premiumBadge: {
    fontSize: 10,
    color: '#ff9800',
    fontWeight: '600',
    marginTop: 4,
  },
  configSection: {
    marginTop: 8,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 6,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  inputHint: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '500',
  },
  submitButton: {
    backgroundColor: '#2196F3',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
});
