import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

interface TimelineStep {
  id: number;
  name: string;
  step_type: string;
  duration: number;
  order: number;
  status: 'completed' | 'current' | 'upcoming';
}

interface RoomTimelineProps {
  steps: TimelineStep[];
  currentStepOrder: number;
  totalDuration?: number;
}

const RoomTimeline: React.FC<RoomTimelineProps> = ({ 
  steps, 
  currentStepOrder, 
  totalDuration 
}) => {
  // 计算总时长（如果没有提供）
  const calculatedTotalDuration = totalDuration || steps.reduce((sum, step) => sum + step.duration, 0);
  
  // 为每个环节计算状态
  const stepsWithStatus = steps.map(step => ({
    ...step,
    status: step.order < currentStepOrder ? 'completed' as const :
            step.order === currentStepOrder ? 'current' as const :
            'upcoming' as const
  }));

  // 获取环节类型的显示名称和图标
  const getStepDisplay = (stepType: string) => {
    const displays: Record<string, { name: string; icon: string; color: string }> = {
      'CHAT': { name: '自由聊天', icon: '💬', color: '#28a745' },
      'GAME_PICTIONARY': { name: '你画我猜', icon: '🎨', color: '#007bff' },
      'GAME_UNDERCOVER': { name: '谁是卧底', icon: '🕵️', color: '#dc3545' },
      'GAME_DRAWING': { name: '绘画游戏', icon: '✏️', color: '#ffc107' },
      'BREAK': { name: '休息时间', icon: '☕', color: '#6c757d' },
      'PRESENTATION': { name: '展示环节', icon: '📊', color: '#17a2b8' },
    };
    return displays[stepType] || { name: stepType, icon: '📋', color: '#6c757d' };
  };

  // 格式化时间显示
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes > 0) {
      return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
    }
    return `${seconds}秒`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>📅 活动安排</Text>
        <Text style={styles.totalTime}>
          总时长: {formatDuration(calculatedTotalDuration)}
        </Text>
      </View>
      
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.timelineContainer}
        contentContainerStyle={styles.timelineContent}
      >
        {stepsWithStatus.map((step, index) => {
          const display = getStepDisplay(step.step_type);
          const isLast = index === stepsWithStatus.length - 1;
          
          return (
            <View key={step.id} style={styles.stepContainer}>
              {/* 环节卡片 */}
              <View style={[
                styles.stepCard,
                step.status === 'completed' && styles.completedCard,
                step.status === 'current' && styles.currentCard,
                step.status === 'upcoming' && styles.upcomingCard,
              ]}>
                <View style={styles.stepHeader}>
                  <Text style={styles.stepIcon}>{display.icon}</Text>
                  <View style={styles.stepInfo}>
                    <Text style={[
                      styles.stepName,
                      step.status === 'completed' && styles.completedText,
                      step.status === 'current' && styles.currentText,
                    ]}>
                      {step.name}
                    </Text>
                    <Text style={styles.stepType}>{display.name}</Text>
                  </View>
                </View>
                
                <View style={styles.stepFooter}>
                  <Text style={styles.stepDuration}>
                    {formatDuration(step.duration)}
                  </Text>
                  <View style={[
                    styles.statusIndicator,
                    { backgroundColor: step.status === 'completed' ? '#28a745' :
                                      step.status === 'current' ? '#007bff' :
                                      '#6c757d' }
                  ]} />
                </View>
              </View>
              
              {/* 连接线 */}
              {!isLast && (
                <View style={[
                  styles.connector,
                  step.status === 'completed' && styles.completedConnector,
                ]} />
              )}
            </View>
          );
        })}
      </ScrollView>
      
      {/* 进度指示器 */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill,
              { 
                width: `${Math.min(100, (currentStepOrder / Math.max(1, steps.length)) * 100)}%` 
              }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>
          {currentStepOrder > 0 ? `${currentStepOrder}/${steps.length}` : `0/${steps.length}`} 环节
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalTime: {
    fontSize: 14,
    color: '#666',
    backgroundColor: '#e9ecef',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  timelineContainer: {
    marginBottom: 16,
  },
  timelineContent: {
    paddingHorizontal: 4,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    minWidth: 140,
    marginHorizontal: 4,
    borderWidth: 2,
    borderColor: '#e9ecef',
  },
  completedCard: {
    borderColor: '#28a745',
    backgroundColor: '#f8fff9',
  },
  currentCard: {
    borderColor: '#007bff',
    backgroundColor: '#f0f8ff',
    elevation: 4,
    shadowColor: '#007bff',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  upcomingCard: {
    borderColor: '#e9ecef',
    backgroundColor: '#fff',
  },
  stepHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  stepIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  stepInfo: {
    flex: 1,
  },
  stepName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 2,
  },
  stepType: {
    fontSize: 12,
    color: '#666',
  },
  completedText: {
    color: '#28a745',
  },
  currentText: {
    color: '#007bff',
  },
  stepFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepDuration: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  connector: {
    width: 20,
    height: 2,
    backgroundColor: '#e9ecef',
    marginHorizontal: 4,
  },
  completedConnector: {
    backgroundColor: '#28a745',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: '#e9ecef',
    borderRadius: 3,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#007bff',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
    minWidth: 60,
    textAlign: 'right',
  },
});

export default RoomTimeline;
