/**
 * 美化的输入框组件
 * 
 * 提供统一的输入框样式和交互效果
 */

import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  Animated,
} from 'react-native';
import { theme } from '../../styles/theme';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  containerStyle?: ViewStyle;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'small' | 'medium' | 'large';
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  containerStyle,
  variant = 'outlined',
  size = 'medium',
  style,
  onFocus,
  onBlur,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [borderAnim] = useState(new Animated.Value(0));

  const handleFocus = (e: any) => {
    setIsFocused(true);
    Animated.timing(borderAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    Animated.timing(borderAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
    onBlur?.(e);
  };

  const getContainerStyle = () => {
    const baseStyle = [styles.container];
    if (variant === 'filled') {
      baseStyle.push(styles.filledContainer);
    }
    return baseStyle;
  };

  const getInputStyle = () => {
    const baseStyle = [styles.input, styles[size], styles[variant]];
    
    if (error) {
      baseStyle.push(styles.inputError);
    } else if (isFocused) {
      baseStyle.push(styles.inputFocused);
    }
    
    return baseStyle;
  };

  const animatedBorderColor = borderAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [theme.colors.border, theme.colors.primary],
  });

  return (
    <View style={[...getContainerStyle(), containerStyle]}>
      {label && (
        <Text style={[
          styles.label,
          isFocused && styles.labelFocused,
          error && styles.labelError
        ]}>
          {label}
        </Text>
      )}
      
      <Animated.View style={[
        styles.inputContainer,
        variant === 'outlined' && {
          borderColor: error ? theme.colors.error : animatedBorderColor,
        }
      ]}>
        <TextInput
          style={[...getInputStyle(), style]}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholderTextColor={theme.colors.gray500}
          {...props}
        />
      </Animated.View>
      
      {(error || helperText) && (
        <Text style={[
          styles.helperText,
          error && styles.errorText
        ]}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  filledContainer: {
    backgroundColor: theme.colors.gray50,
    borderRadius: theme.borderRadius.lg,
  },
  
  label: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  labelFocused: {
    color: theme.colors.primary,
  },
  labelError: {
    color: theme.colors.error,
  },
  
  inputContainer: {
    borderRadius: theme.borderRadius.lg,
  },
  
  input: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    paddingHorizontal: theme.spacing.lg,
  },
  
  // 尺寸样式
  small: {
    height: 36,
    fontSize: theme.typography.fontSize.sm,
  },
  medium: {
    height: 44,
  },
  large: {
    height: 52,
    fontSize: theme.typography.fontSize.lg,
  },
  
  // 变体样式
  default: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  filled: {
    backgroundColor: theme.colors.gray50,
  },
  outlined: {
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.border,
  },
  
  inputFocused: {
    borderColor: theme.colors.primary,
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  
  helperText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  errorText: {
    color: theme.colors.error,
  },
});
