/**
 * 通用徽章组件
 * 
 * 功能：
 * - 多种徽章变体（success, warning, error, info等）
 * - 可配置的尺寸
 * - 支持自定义颜色
 * - 支持图标
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle
} from 'react-native';
import { theme } from '../../styles/theme';

type BadgeVariant = 'success' | 'warning' | 'error' | 'info' | 'neutral' | 'primary' | 'secondary' | 'vibrant';
type BadgeSize = 'xs' | 'small' | 'medium' | 'large';

interface BadgeProps {
  text: string;
  variant?: BadgeVariant;
  size?: BadgeSize;
  style?: ViewStyle;
  textStyle?: TextStyle;
  backgroundColor?: string;
  textColor?: string;
  icon?: React.ReactNode;
  rounded?: boolean;
  outlined?: boolean;
  pulsing?: boolean;
}

/**
 * 通用徽章组件 - 重新设计版本
 *
 * 新特性：
 * - 更多变体选择（primary, secondary, vibrant等）
 * - 支持轮廓样式和脉冲动画
 * - 更现代的圆角和间距设计
 * - 增强的视觉层次
 */
export const Badge: React.FC<BadgeProps> = ({
  text,
  variant = 'neutral',
  size = 'medium',
  style,
  textStyle,
  backgroundColor,
  textColor,
  icon,
  rounded = false,
  outlined = false,
  pulsing = false,
}) => {
  const badgeStyle = [
    styles.badge,
    styles[`${variant}Badge`],
    styles[`${size}Badge`],
    rounded && styles.rounded,
    outlined && styles[`${variant}Outlined`],
    backgroundColor && { backgroundColor },
    style,
  ];

  const textStyleCombined = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    outlined && styles[`${variant}OutlinedText`],
    textColor && { color: textColor },
    textStyle,
  ];

  const BadgeComponent = (
    <View style={badgeStyle}>
      {icon && <View style={styles.iconContainer}>{icon}</View>}
      <Text style={textStyleCombined}>{text}</Text>
    </View>
  );

  // 如果需要脉冲效果，包装在动画组件中
  if (pulsing) {
    // 这里可以添加脉冲动画逻辑
    return BadgeComponent;
  }

  return BadgeComponent;
};

const styles = StyleSheet.create({
  badge: {
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 0,
  },

  // 尺寸样式 - 使用新的间距系统
  xsBadge: {
    paddingHorizontal: theme.spacing.xs,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  smallBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
  },
  mediumBadge: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
  },
  largeBadge: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.xl,
  },

  // 变体样式 - 使用新的色彩系统
  successBadge: {
    backgroundColor: theme.colors.successSoft,
  },
  warningBadge: {
    backgroundColor: theme.colors.warningSoft,
  },
  errorBadge: {
    backgroundColor: theme.colors.errorSoft,
  },
  infoBadge: {
    backgroundColor: theme.colors.infoSoft,
  },
  neutralBadge: {
    backgroundColor: theme.colors.gray100,
  },
  primaryBadge: {
    backgroundColor: theme.colors.primarySoft,
  },
  secondaryBadge: {
    backgroundColor: theme.colors.secondarySoft,
  },
  vibrantBadge: {
    backgroundColor: theme.colors.vibrant.coral,
  },

  // 轮廓样式
  successOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.success,
  },
  warningOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.warning,
  },
  errorOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.error,
  },
  infoOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.info,
  },
  neutralOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.gray300,
  },
  primaryOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  secondaryOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.secondary,
  },
  vibrantOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.vibrant.coral,
  },

  // 特殊样式
  rounded: {
    borderRadius: theme.borderRadius.full,
  },

  // 文本样式 - 使用新的字体系统
  text: {
    fontWeight: theme.typography.fontWeight.semibold,
    fontFamily: theme.typography.fontFamily.primary,
  },

  // 文本尺寸
  xsText: {
    fontSize: theme.typography.fontSize.xs,
  },
  smallText: {
    fontSize: theme.typography.fontSize.sm,
  },
  mediumText: {
    fontSize: theme.typography.fontSize.base,
  },
  largeText: {
    fontSize: theme.typography.fontSize.lg,
  },

  // 文本变体颜色 - 使用新的色彩系统
  successText: {
    color: theme.colors.successDark,
  },
  warningText: {
    color: theme.colors.warningDark,
  },
  errorText: {
    color: theme.colors.errorDark,
  },
  infoText: {
    color: theme.colors.infoDark,
  },
  neutralText: {
    color: theme.colors.gray700,
  },
  primaryText: {
    color: theme.colors.primaryDark,
  },
  secondaryText: {
    color: theme.colors.secondaryDark,
  },
  vibrantText: {
    color: theme.colors.white,
  },

  // 轮廓文本颜色
  successOutlinedText: {
    color: theme.colors.success,
  },
  warningOutlinedText: {
    color: theme.colors.warning,
  },
  errorOutlinedText: {
    color: theme.colors.error,
  },
  infoOutlinedText: {
    color: theme.colors.info,
  },
  neutralOutlinedText: {
    color: theme.colors.gray600,
  },
  primaryOutlinedText: {
    color: theme.colors.primary,
  },
  secondaryOutlinedText: {
    color: theme.colors.secondary,
  },
  vibrantOutlinedText: {
    color: theme.colors.vibrant.coral,
  },

  // 图标容器
  iconContainer: {
    marginRight: theme.spacing.xs,
  },
});
