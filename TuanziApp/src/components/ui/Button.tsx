/**
 * 通用按钮组件
 * 
 * 功能：
 * - 多种按钮变体（primary, secondary, outline, danger等）
 * - 可配置的尺寸（small, medium, large）
 * - 支持加载状态
 * - 支持禁用状态
 * - 支持图标
 */

import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  View,
  Animated
} from 'react-native';
import { theme } from '../../styles/theme';

type ButtonVariant = 'primary' | 'secondary' | 'tertiary' | 'outline' | 'ghost' | 'danger' | 'success' | 'warning' | 'vibrant';
type ButtonSize = 'small' | 'medium' | 'large' | 'xl';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  rounded?: boolean;
  shadow?: boolean;
  hapticFeedback?: boolean;
}

/**
 * 通用按钮组件 - 重新设计版本
 *
 * 新特性：
 * - 更多变体选择（primary, secondary, tertiary, outline, ghost, vibrant等）
 * - 增强的视觉反馈和动画效果
 * - 更现代的阴影和圆角设计
 * - 支持全宽度和自定义圆角
 */
export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  rounded = false,
  shadow = true,
  hapticFeedback = true,
}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;

  const buttonStyle = [
    styles.button,
    styles[`${variant}Button`],
    styles[`${size}Button`],
    fullWidth && styles.fullWidth,
    rounded && styles.rounded,
    shadow && !disabled && styles[`${variant}Shadow`],
    disabled && styles.disabledButton,
    style,
  ];

  const textStyleCombined = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    disabled && styles.disabledText,
    textStyle,
  ];

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    if (!disabled && !loading) {
      onPress();
    }
  };

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="small"
            color={getActivityIndicatorColor(variant)}
          />
          <Text style={[textStyleCombined, { marginLeft: theme.spacing.sm }]}>
            {title}
          </Text>
        </View>
      );
    }

    if (icon) {
      return (
        <View style={styles.iconContainer}>
          {iconPosition === 'left' && icon}
          <Text style={textStyleCombined}>{title}</Text>
          {iconPosition === 'right' && icon}
        </View>
      );
    }

    return <Text style={textStyleCombined}>{title}</Text>;
  };

  return (
    <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
      <TouchableOpacity
        style={buttonStyle}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={disabled ? 1 : 0.8}
        disabled={disabled || loading}
      >
        {renderContent()}
      </TouchableOpacity>
    </Animated.View>
  );
};

// 辅助函数：获取加载指示器颜色
const getActivityIndicatorColor = (variant: ButtonVariant): string => {
  switch (variant) {
    case 'outline':
    case 'ghost':
      return theme.colors.primary;
    case 'danger':
      return theme.colors.white;
    case 'success':
      return theme.colors.white;
    case 'warning':
      return theme.colors.white;
    case 'vibrant':
      return theme.colors.white;
    default:
      return theme.colors.white;
  }
};

const styles = StyleSheet.create({
  button: {
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderWidth: 0,
  },

  // 尺寸样式 - 更现代的间距
  smallButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.lg,
    minHeight: 36,
  },
  mediumButton: {
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.xl,
    minHeight: 44,
  },
  largeButton: {
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing['2xl'],
    minHeight: 52,
  },
  xlButton: {
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing['3xl'],
    minHeight: 60,
  },

  // 主要变体样式 - 使用新的色彩系统
  primaryButton: {
    backgroundColor: theme.colors.primary,
  },
  secondaryButton: {
    backgroundColor: theme.colors.secondary,
  },
  tertiaryButton: {
    backgroundColor: theme.colors.tertiary,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  ghostButton: {
    backgroundColor: 'transparent',
  },
  dangerButton: {
    backgroundColor: theme.colors.error,
  },
  successButton: {
    backgroundColor: theme.colors.success,
  },
  warningButton: {
    backgroundColor: theme.colors.warning,
  },
  vibrantButton: {
    backgroundColor: theme.colors.vibrant.coral,
  },

  // 阴影样式 - 为每个变体定制
  primaryShadow: {
    ...theme.shadows.md,
    shadowColor: theme.colors.primary,
  },
  secondaryShadow: {
    ...theme.shadows.md,
    shadowColor: theme.colors.secondary,
  },
  tertiaryShadow: {
    ...theme.shadows.md,
    shadowColor: theme.colors.tertiary,
  },
  dangerShadow: {
    ...theme.shadows.md,
    shadowColor: theme.colors.error,
  },
  successShadow: {
    ...theme.shadows.md,
    shadowColor: theme.colors.success,
  },
  warningShadow: {
    ...theme.shadows.md,
    shadowColor: theme.colors.warning,
  },
  vibrantShadow: {
    ...theme.shadows.colored,
  },
  outlineShadow: {
    ...theme.shadows.sm,
  },
  ghostShadow: {
    ...theme.shadows.none,
  },

  // 特殊样式
  fullWidth: {
    width: '100%',
  },
  rounded: {
    borderRadius: theme.borderRadius.full,
  },

  // 禁用状态
  disabledButton: {
    backgroundColor: theme.colors.gray300,
    borderColor: theme.colors.gray300,
  },

  // 文本样式 - 使用新的字体系统
  text: {
    fontWeight: theme.typography.fontWeight.semibold,
    fontFamily: theme.typography.fontFamily.primary,
  },

  // 文本尺寸
  smallText: {
    fontSize: theme.typography.fontSize.sm,
  },
  mediumText: {
    fontSize: theme.typography.fontSize.base,
  },
  largeText: {
    fontSize: theme.typography.fontSize.lg,
  },
  xlText: {
    fontSize: theme.typography.fontSize.xl,
  },

  // 文本变体颜色 - 使用新的色彩系统
  primaryText: {
    color: theme.colors.white,
  },
  secondaryText: {
    color: theme.colors.white,
  },
  tertiaryText: {
    color: theme.colors.white,
  },
  outlineText: {
    color: theme.colors.primary,
  },
  ghostText: {
    color: theme.colors.primary,
  },
  dangerText: {
    color: theme.colors.white,
  },
  successText: {
    color: theme.colors.white,
  },
  warningText: {
    color: theme.colors.white,
  },
  vibrantText: {
    color: theme.colors.white,
  },

  // 禁用文本
  disabledText: {
    color: theme.colors.gray500,
  },

  // 容器样式
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
});
