/**
 * 通用卡片组件
 * 
 * 功能：
 * - 提供统一的卡片样式
 * - 支持阴影、圆角、边框等自定义
 * - 可配置的内边距和外边距
 * - 支持点击事件
 */

import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TouchableOpacityProps,
  Animated
} from 'react-native';
import { theme } from '../../styles/theme';

type CardVariant = 'default' | 'elevated' | 'outlined' | 'filled' | 'vibrant';
type CardSize = 'small' | 'medium' | 'large';

interface CardProps extends TouchableOpacityProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: CardVariant;
  size?: CardSize;
  padding?: number;
  margin?: number;
  borderRadius?: number;
  backgroundColor?: string;
  shadowEnabled?: boolean;
  borderColor?: string;
  borderWidth?: number;
  onPress?: () => void;
  pressable?: boolean;
  glowEffect?: boolean;
}

/**
 * 通用卡片组件 - 重新设计版本
 *
 * 新特性：
 * - 多种变体（default, elevated, outlined, filled, vibrant）
 * - 预设尺寸（small, medium, large）
 * - 增强的视觉反馈和动画
 * - 发光效果选项
 * - 更现代的阴影和圆角
 */
export const Card: React.FC<CardProps> = ({
  children,
  style,
  variant = 'default',
  size = 'medium',
  padding,
  margin = 0,
  borderRadius,
  backgroundColor,
  shadowEnabled,
  borderColor,
  borderWidth,
  onPress,
  pressable = !!onPress,
  glowEffect = false,
  ...props
}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;

  // 根据变体和尺寸确定默认值
  const defaultPadding = padding ?? getDefaultPadding(size);
  const defaultBorderRadius = borderRadius ?? getDefaultBorderRadius(variant);
  const defaultBackgroundColor = backgroundColor ?? getDefaultBackgroundColor(variant);
  const defaultShadowEnabled = shadowEnabled ?? getShadowEnabled(variant);

  const cardStyle: ViewStyle = {
    backgroundColor: defaultBackgroundColor,
    borderRadius: defaultBorderRadius,
    padding: defaultPadding,
    margin,
    borderColor: borderColor ?? getBorderColor(variant),
    borderWidth: borderWidth ?? getBorderWidth(variant),
    ...(defaultShadowEnabled && getCardShadow(variant, glowEffect)),
    ...style,
  };

  const handlePressIn = () => {
    if (pressable) {
      Animated.spring(scaleValue, {
        toValue: 0.98,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (pressable) {
      Animated.spring(scaleValue, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }
  };

  if (onPress || pressable) {
    return (
      <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
        <TouchableOpacity
          style={cardStyle}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={0.9}
          {...props}
        >
          {children}
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <View style={cardStyle}>
      {children}
    </View>
  );
};

// 辅助函数
const getDefaultPadding = (size: CardSize): number => {
  switch (size) {
    case 'small':
      return theme.spacing.md;
    case 'medium':
      return theme.spacing.lg;
    case 'large':
      return theme.spacing.xl;
    default:
      return theme.spacing.lg;
  }
};

const getDefaultBorderRadius = (variant: CardVariant): number => {
  switch (variant) {
    case 'default':
    case 'elevated':
      return theme.borderRadius.xl;
    case 'outlined':
    case 'filled':
      return theme.borderRadius.lg;
    case 'vibrant':
      return theme.borderRadius['2xl'];
    default:
      return theme.borderRadius.xl;
  }
};

const getDefaultBackgroundColor = (variant: CardVariant): string => {
  switch (variant) {
    case 'default':
    case 'elevated':
    case 'outlined':
      return theme.colors.surface;
    case 'filled':
      return theme.colors.gray50;
    case 'vibrant':
      return theme.colors.primarySoft;
    default:
      return theme.colors.surface;
  }
};

const getShadowEnabled = (variant: CardVariant): boolean => {
  switch (variant) {
    case 'elevated':
    case 'vibrant':
      return true;
    case 'outlined':
    case 'filled':
      return false;
    case 'default':
    default:
      return true;
  }
};

const getBorderColor = (variant: CardVariant): string | undefined => {
  switch (variant) {
    case 'outlined':
      return theme.colors.border;
    case 'vibrant':
      return theme.colors.primary;
    default:
      return undefined;
  }
};

const getBorderWidth = (variant: CardVariant): number => {
  switch (variant) {
    case 'outlined':
      return 1;
    case 'vibrant':
      return 2;
    default:
      return 0;
  }
};

const getCardShadow = (variant: CardVariant, glowEffect: boolean) => {
  if (glowEffect) {
    return variant === 'vibrant' ? theme.shadows.glow : theme.shadows.colored;
  }

  switch (variant) {
    case 'elevated':
      return theme.shadows.lg;
    case 'vibrant':
      return theme.shadows.md;
    case 'default':
    default:
      return theme.shadows.md;
  }
};

const styles = StyleSheet.create({
  // 保留原有样式作为后备
  shadow: {
    ...theme.shadows.md,
  },
});
