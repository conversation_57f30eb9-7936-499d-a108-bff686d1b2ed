// TuanziApp/src/components/OptimisticChatView.tsx

import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Message } from '../types';
import { theme } from '../styles/theme';

interface OptimisticMessage extends Message {
  isOptimistic?: boolean; // 标记为乐观更新的消息
  tempId?: string; // 临时ID，用于匹配服务器确认
  failed?: boolean; // 标记发送失败
}

interface OptimisticChatViewProps {
  messages: Message[];
  onSendMessage: (message: string) => void;
  currentUser: string;
  compact?: boolean;
}

export const OptimisticChatView: React.FC<OptimisticChatViewProps> = ({
  messages,
  onSendMessage,
  currentUser,
  compact = false,
}) => {
  const [inputText, setInputText] = useState('');
  const [optimisticMessages, setOptimisticMessages] = useState<OptimisticMessage[]>([]);
  const flatListRef = useRef<FlatList>(null);
  const messageTimeoutRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // 合并服务器消息和乐观消息
  const allMessages = React.useMemo(() => {
    const serverMessages = messages.map(msg => ({ ...msg, isOptimistic: false }));
    const pendingOptimistic = optimisticMessages.filter(opt => 
      !serverMessages.some(server => 
        server.username === opt.username && 
        server.message === opt.message &&
        Math.abs(new Date(server.timestamp).getTime() - new Date(opt.timestamp).getTime()) < 5000
      )
    );
    
    return [...serverMessages, ...pendingOptimistic]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }, [messages, optimisticMessages]);

  // 清理已确认的乐观消息
  useEffect(() => {
    const confirmedTempIds = new Set<string>();
    
    optimisticMessages.forEach(opt => {
      if (opt.tempId) {
        const isConfirmed = messages.some(server => 
          server.username === opt.username && 
          server.message === opt.message &&
          Math.abs(new Date(server.timestamp).getTime() - new Date(opt.timestamp).getTime()) < 5000
        );
        
        if (isConfirmed) {
          confirmedTempIds.add(opt.tempId);
        }
      }
    });

    if (confirmedTempIds.size > 0) {
      setOptimisticMessages(prev => 
        prev.filter(msg => !msg.tempId || !confirmedTempIds.has(msg.tempId))
      );
      
      // 清理对应的超时器
      confirmedTempIds.forEach(tempId => {
        const timeout = messageTimeoutRef.current.get(tempId);
        if (timeout) {
          clearTimeout(timeout);
          messageTimeoutRef.current.delete(tempId);
        }
      });
    }
  }, [messages, optimisticMessages]);

  const handleSendMessage = useCallback(() => {
    if (!inputText.trim()) return;

    const messageText = inputText.trim();
    const tempId = `temp_${Date.now()}_${Math.random()}`;
    
    // 创建乐观消息
    const optimisticMessage: OptimisticMessage = {
      id: tempId,
      username: currentUser,
      message: messageText,
      timestamp: new Date().toISOString(),
      isOptimistic: true,
      tempId,
    };

    // 立即添加到乐观消息列表
    setOptimisticMessages(prev => [optimisticMessage, ...prev]);
    setInputText('');

    // 发送到服务器
    try {
      onSendMessage(messageText);
    } catch (error) {
      console.error('Failed to send message:', error);
      // 标记消息为失败
      setOptimisticMessages(prev => 
        prev.map(msg => 
          msg.tempId === tempId 
            ? { ...msg, failed: true }
            : msg
        )
      );
    }

    // 设置超时，如果5秒内没有收到服务器确认，标记为失败
    const timeout = setTimeout(() => {
      setOptimisticMessages(prev => 
        prev.map(msg => 
          msg.tempId === tempId 
            ? { ...msg, failed: true }
            : msg
        )
      );
      messageTimeoutRef.current.delete(tempId);
    }, 5000);

    messageTimeoutRef.current.set(tempId, timeout);
  }, [inputText, currentUser, onSendMessage]);

  const retryMessage = useCallback((tempId: string) => {
    const failedMessage = optimisticMessages.find(msg => msg.tempId === tempId);
    if (!failedMessage) return;

    // 重置失败状态
    setOptimisticMessages(prev => 
      prev.map(msg => 
        msg.tempId === tempId 
          ? { ...msg, failed: false }
          : msg
      )
    );

    // 重新发送
    try {
      onSendMessage(failedMessage.message);
    } catch (error) {
      console.error('Failed to retry message:', error);
      setOptimisticMessages(prev => 
        prev.map(msg => 
          msg.tempId === tempId 
            ? { ...msg, failed: true }
            : msg
        )
      );
    }
  }, [optimisticMessages, onSendMessage]);

  const removeFailedMessage = useCallback((tempId: string) => {
    setOptimisticMessages(prev => prev.filter(msg => msg.tempId !== tempId));
    
    const timeout = messageTimeoutRef.current.get(tempId);
    if (timeout) {
      clearTimeout(timeout);
      messageTimeoutRef.current.delete(tempId);
    }
  }, []);

  const renderMessage = ({ item }: { item: OptimisticMessage }) => {
    const isCurrentUser = item.username === currentUser;
    
    return (
      <View style={[
        styles.messageContainer,
        isCurrentUser ? styles.currentUserMessage : styles.otherUserMessage,
        compact && styles.compactMessage,
      ]}>
        {!isCurrentUser && (
          <Text style={styles.username}>{item.username}</Text>
        )}
        <View style={[
          styles.messageBubble,
          isCurrentUser ? styles.currentUserBubble : styles.otherUserBubble,
          item.isOptimistic && styles.optimisticBubble,
          item.failed && styles.failedBubble,
        ]}>
          <Text style={[
            styles.messageText,
            isCurrentUser ? styles.currentUserText : styles.otherUserText,
            item.failed && styles.failedText,
          ]}>
            {item.message}
          </Text>
          {item.isOptimistic && !item.failed && (
            <Text style={styles.sendingIndicator}>发送中...</Text>
          )}
          {item.failed && (
            <View style={styles.failedActions}>
              <TouchableOpacity 
                style={styles.retryButton}
                onPress={() => retryMessage(item.tempId!)}
              >
                <Text style={styles.retryText}>重试</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.removeButton}
                onPress={() => removeFailedMessage(item.tempId!)}
              >
                <Text style={styles.removeText}>删除</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
        <Text style={styles.timestamp}>
          {new Date(item.timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </Text>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <FlatList
        ref={flatListRef}
        data={allMessages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id.toString()}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        inverted
        showsVerticalScrollIndicator={false}
      />
      
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={setInputText}
          placeholder="输入消息..."
          placeholderTextColor={theme.colors.textSecondary}
          multiline
          maxLength={500}
          onSubmitEditing={handleSendMessage}
          blurOnSubmit={false}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            !inputText.trim() && styles.sendButtonDisabled
          ]}
          onPress={handleSendMessage}
          disabled={!inputText.trim()}
        >
          <Text style={[
            styles.sendButtonText,
            !inputText.trim() && styles.sendButtonTextDisabled
          ]}>
            发送
          </Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  messagesContent: {
    paddingVertical: 16,
  },
  messageContainer: {
    marginBottom: 12,
    maxWidth: '80%',
  },
  compactMessage: {
    marginBottom: 8,
  },
  currentUserMessage: {
    alignSelf: 'flex-end',
    alignItems: 'flex-end',
  },
  otherUserMessage: {
    alignSelf: 'flex-start',
    alignItems: 'flex-start',
  },
  username: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
  },
  messageBubble: {
    padding: 12,
    borderRadius: 16,
    maxWidth: '100%',
  },
  currentUserBubble: {
    backgroundColor: theme.colors.primary,
  },
  otherUserBubble: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  optimisticBubble: {
    opacity: 0.7,
  },
  failedBubble: {
    backgroundColor: theme.colors.error + '20',
    borderColor: theme.colors.error,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  currentUserText: {
    color: theme.colors.surface,
  },
  otherUserText: {
    color: theme.colors.textPrimary,
  },
  failedText: {
    color: theme.colors.error,
  },
  sendingIndicator: {
    fontSize: 10,
    color: theme.colors.surface,
    opacity: 0.7,
    marginTop: 2,
    fontStyle: 'italic',
  },
  failedActions: {
    flexDirection: 'row',
    marginTop: 4,
    gap: 8,
  },
  retryButton: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    backgroundColor: theme.colors.primary,
    borderRadius: 4,
  },
  retryText: {
    fontSize: 10,
    color: theme.colors.surface,
    fontWeight: 'bold',
  },
  removeButton: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    backgroundColor: theme.colors.error,
    borderRadius: 4,
  },
  removeText: {
    fontSize: 10,
    color: theme.colors.surface,
    fontWeight: 'bold',
  },
  timestamp: {
    fontSize: 10,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    maxHeight: 100,
    fontSize: 16,
    color: theme.colors.textPrimary,
    backgroundColor: theme.colors.background,
  },
  sendButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: theme.colors.gray400,
  },
  sendButtonText: {
    color: theme.colors.surface,
    fontWeight: 'bold',
    fontSize: 14,
  },
  sendButtonTextDisabled: {
    color: theme.colors.textSecondary,
  },
});
