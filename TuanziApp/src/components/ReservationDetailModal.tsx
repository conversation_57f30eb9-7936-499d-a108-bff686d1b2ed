// TuanziApp/src/components/ReservationDetailModal.tsx

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Reservation } from '../types';
import { theme } from '../styles/theme';
import { getReservationDetails, cancelReservation, canCancelReservation, canEditReservation, isReservationExpired, handleApiError } from '../api/calendarApi';
import { EditReservationModal } from './EditReservationModal';

interface ReservationDetailModalProps {
  visible: boolean;
  onClose: () => void;
  reservation: Reservation | null;
  onReservationUpdated?: () => void; // 预约更新后的回调
}

interface ReservationDetails extends Reservation {
  template_details?: {
    name: string;
    description: string;
    steps: Array<{
      id: number;
      name: string;
      step_type: string;
      duration: number;
      order: number;
    }>;
  };
  participants?: Array<{
    username: string;
    role: string;
    joined_at: string;
  }>;
}

export const ReservationDetailModal: React.FC<ReservationDetailModalProps> = ({
  visible,
  onClose,
  reservation,
  onReservationUpdated,
}) => {
  const [details, setDetails] = useState<ReservationDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [cancelling, setCancelling] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    if (visible && reservation) {
      fetchReservationDetails();
    }
  }, [visible, reservation]);

  const fetchReservationDetails = async () => {
    if (!reservation) return;

    try {
      setLoading(true);
      const detailsData = await getReservationDetails(reservation.id);
      setDetails(detailsData);
    } catch (error) {
      console.error('Failed to fetch reservation details:', error);
      // 如果获取详情失败，至少显示基本信息
      setDetails(reservation as ReservationDetails);
    } finally {
      setLoading(false);
    }
  };

  const handleEditReservation = () => {
    if (!reservation) return;

    const startTime = new Date(reservation.start_time);
    const endTime = new Date(reservation.end_time);

    // 检查是否已过期
    if (isReservationExpired(endTime)) {
      Alert.alert(
        '无法修改预约',
        '预约已过期，无法修改。',
        [{ text: '确定', style: 'default' }]
      );
      return;
    }

    // 检查是否可以修改
    if (!canEditReservation(startTime)) {
      const now = new Date();
      const timeDifference = startTime.getTime() - now.getTime();
      const minutesUntilStart = Math.floor(timeDifference / (1000 * 60));

      if (minutesUntilStart > 0) {
        Alert.alert(
          '无法修改预约',
          `预约将在${minutesUntilStart}分钟后开始，距离开始时间不足1小时，无法修改预约。如需修改，请联系客服。`,
          [{ text: '确定', style: 'default' }]
        );
      } else {
        Alert.alert(
          '无法修改预约',
          '预约已经开始，无法修改。',
          [{ text: '确定', style: 'default' }]
        );
      }
      return;
    }

    setShowEditModal(true);
  };

  const handleEditCompleted = () => {
    setShowEditModal(false);
    // 重新获取预约详情
    fetchReservationDetails();
    onReservationUpdated?.();
  };

  const handleCancelReservation = () => {
    if (!reservation) return;

    const startTime = new Date(reservation.start_time);
    const endTime = new Date(reservation.end_time);

    // 检查是否已过期
    if (isReservationExpired(endTime)) {
      Alert.alert(
        '无法取消预约',
        '预约已过期，无法取消。',
        [{ text: '确定', style: 'default' }]
      );
      return;
    }

    // 检查是否可以取消
    if (!canCancelReservation(startTime)) {
      const now = new Date();
      const timeDifference = startTime.getTime() - now.getTime();
      const minutesUntilStart = Math.floor(timeDifference / (1000 * 60));

      if (minutesUntilStart > 0) {
        Alert.alert(
          '无法取消预约',
          `预约将在${minutesUntilStart}分钟后开始，距离开始时间不足30分钟，无法取消预约。如需取消，请联系客服。`,
          [{ text: '确定', style: 'default' }]
        );
      } else {
        Alert.alert(
          '无法取消预约',
          '预约已经开始，无法取消。',
          [{ text: '确定', style: 'default' }]
        );
      }
      return;
    }

    // 正常取消流程
    Alert.alert(
      '取消预约',
      '确定要取消这个预约吗？此操作无法撤销。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '确定取消',
          style: 'destructive',
          onPress: async () => {
            try {
              setCancelling(true);
              await cancelReservation(reservation.id);
              Alert.alert('成功', '预约已取消');
              onReservationUpdated?.();
              onClose();
            } catch (error) {
              console.error('Failed to cancel reservation:', error);
              Alert.alert('错误', handleApiError(error));
            } finally {
              setCancelling(false);
            }
          },
        },
      ]
    );
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
  };

  const formatDuration = (hours: number) => {
    if (hours < 24) {
      return `${hours} 小时`;
    } else {
      const days = Math.floor(hours / 24);
      const remainingHours = hours % 24;
      return remainingHours > 0 ? `${days} 天 ${remainingHours} 小时` : `${days} 天`;
    }
  };

  const getStepTypeDisplay = (stepType: string) => {
    const stepTypeMap: { [key: string]: string } = {
      'FREE_CHAT': '自由聊天',
      'GAME_PICTIONARY': '你画我猜',
      'DRAWING_GAME': '你画我猜',
      'UNDERCOVER_GAME': '谁是卧底',
      'VOTING': '投票环节',
      'ANNOUNCEMENT': '公告环节',
    };
    return stepTypeMap[stepType] || stepType;
  };

  const getReservationStatus = () => {
    if (!details) return '未知';

    const now = new Date();
    const startTime = new Date(details.start_time);
    const endTime = new Date(details.end_time);

    if (endTime < now) {
      return '已过期';
    } else if (startTime <= now && now <= endTime) {
      return '进行中';
    } else if (startTime > now) {
      const timeDiff = startTime.getTime() - now.getTime();
      const minutesUntilStart = Math.floor(timeDiff / (1000 * 60));

      if (minutesUntilStart <= 30) {
        return '即将开始';
      } else {
        return '未开始';
      }
    }

    return '未知';
  };

  if (!reservation) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        {/* 模态框头部 */}
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>预约详情</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* 内容区域 */}
        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={styles.loadingText}>加载详情...</Text>
            </View>
          ) : (
            <>
              {/* 基本信息 */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>基本信息</Text>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>房间名称:</Text>
                  <Text style={styles.infoValue}>{details?.name}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>房间代码:</Text>
                  <Text style={styles.infoValue}>{details?.room_code}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>房主:</Text>
                  <Text style={styles.infoValue}>{details?.host_username}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>状态:</Text>
                  <Text style={[
                    styles.infoValue,
                    getReservationStatus() === '已过期' && styles.expiredStatus,
                    getReservationStatus() === '即将开始' && styles.upcomingStatus,
                    getReservationStatus() === '进行中' && styles.activeStatus
                  ]}>
                    {getReservationStatus()}
                  </Text>
                </View>
              </View>

              {/* 时间信息 */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>时间安排</Text>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>开始时间:</Text>
                  <Text style={styles.infoValue}>{formatDateTime(details?.start_time || '')}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>结束时间:</Text>
                  <Text style={styles.infoValue}>{formatDateTime(details?.end_time || '')}</Text>
                </View>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>持续时长:</Text>
                  <Text style={styles.infoValue}>{formatDuration(details?.duration_hours || 0)}</Text>
                </View>
              </View>

              {/* 参与者信息 */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>参与者</Text>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>当前人数:</Text>
                  <Text style={styles.infoValue}>
                    {details?.current_participants} / {details?.max_participants}
                  </Text>
                </View>
                {details?.participants && details.participants.length > 0 && (
                  <View style={styles.participantsList}>
                    {details.participants.map((participant, index) => (
                      <View key={index} style={styles.participantItem}>
                        <Text style={styles.participantName}>{participant.username}</Text>
                        <Text style={styles.participantRole}>
                          {participant.role === 'HOST' ? '房主' : '参与者'}
                        </Text>
                      </View>
                    ))}
                  </View>
                )}
              </View>

              {/* 模板信息 */}
              {details?.template_details && (
                <View style={styles.section}>
                  <Text style={styles.sectionTitle}>活动模板</Text>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>模板名称:</Text>
                    <Text style={styles.infoValue}>{details.template_details.name}</Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Text style={styles.infoLabel}>模板描述:</Text>
                    <Text style={styles.infoValue}>{details.template_details.description}</Text>
                  </View>
                  
                  {details.template_details.steps && details.template_details.steps.length > 0 && (
                    <View style={styles.stepsContainer}>
                      <Text style={styles.stepsTitle}>活动环节:</Text>
                      {details.template_details.steps.map((step, index) => (
                        <View key={step.id} style={styles.stepItem}>
                          <Text style={styles.stepNumber}>{index + 1}.</Text>
                          <View style={styles.stepContent}>
                            <Text style={styles.stepName}>{step.name}</Text>
                            <Text style={styles.stepType}>
                              {getStepTypeDisplay(step.step_type)} • {Math.floor(step.duration / 60)} 分钟
                            </Text>
                          </View>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              )}
            </>
          )}
        </ScrollView>

        {/* 底部操作按钮 */}
        <View style={styles.modalFooter}>
          <TouchableOpacity
            style={[
              styles.editButton,
              { marginRight: 12 },
              (cancelling || loading) && styles.buttonDisabled
            ]}
            onPress={handleEditReservation}
            disabled={cancelling || loading}
          >
            <Text style={styles.editButtonText}>修改预约</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.cancelButton, cancelling && styles.buttonDisabled]}
            onPress={handleCancelReservation}
            disabled={cancelling}
          >
            {cancelling ? (
              <ActivityIndicator size="small" color={theme.colors.surface} />
            ) : (
              <Text style={styles.cancelButtonText}>取消预约</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* 编辑预约模态框 */}
        <EditReservationModal
          visible={showEditModal}
          onClose={() => setShowEditModal(false)}
          reservation={reservation}
          onReservationUpdated={handleEditCompleted}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  section: {
    marginVertical: 16,
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    width: 80,
    flexShrink: 0,
  },
  infoValue: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    flex: 1,
    fontWeight: '500',
  },
  participantsList: {
    marginTop: 8,
  },
  participantItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  participantName: {
    fontSize: 14,
    color: theme.colors.textPrimary,
  },
  participantRole: {
    fontSize: 12,
    color: theme.colors.primary,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  stepsContainer: {
    marginTop: 12,
  },
  stepsTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 8,
  },
  stepItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  stepNumber: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    width: 20,
    flexShrink: 0,
  },
  stepContent: {
    flex: 1,
  },
  stepName: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    fontWeight: '500',
  },
  stepType: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  modalFooter: {
    flexDirection: 'row',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  editButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.error,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
  buttonDisabled: {
    backgroundColor: theme.colors.gray400,
  },
  expiredStatus: {
    color: theme.colors.error || '#ff0000',
    fontWeight: 'bold',
  },
  upcomingStatus: {
    color: theme.colors.warning || '#ff9500',
    fontWeight: 'bold',
  },
  activeStatus: {
    color: theme.colors.success || '#00ff00',
    fontWeight: 'bold',
  },
});
