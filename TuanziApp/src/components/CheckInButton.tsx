/**
 * 签到按钮组件
 * 
 * 提供签到功能的交互按钮，包括签到状态显示和签到操作
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {
  getCheckInStatus,
  performCheckIn,
  formatConsecutiveDays,
  formatTotalCheckIns,
  CheckInStatus,
} from '../api/checkInApi';
import { theme } from '../styles/theme';

interface CheckInButtonProps {
  onCheckInSuccess?: (result: any) => void;
  style?: any;
  compact?: boolean; // 紧凑模式，只显示按钮
}

export const CheckInButton: React.FC<CheckInButtonProps> = ({
  onCheckInSuccess,
  style,
  compact = false,
}) => {
  const [checkInStatus, setCheckInStatus] = useState<CheckInStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);

  useEffect(() => {
    loadCheckInStatus();
  }, []);

  const loadCheckInStatus = async () => {
    try {
      setLoading(true);
      const status = await getCheckInStatus();
      setCheckInStatus(status);
    } catch (error) {
      console.error('Failed to load check-in status:', error);
      // 不显示错误提示，静默失败
    } finally {
      setLoading(false);
    }
  };

  const handleCheckIn = async () => {
    if (!checkInStatus || checkInStatus.has_checked_in_today) {
      return;
    }

    try {
      setChecking(true);
      const result = await performCheckIn();
      
      Alert.alert(
        '签到成功！',
        `${result.message}\n${formatConsecutiveDays(result.consecutive_days)}`,
        [{ text: '确定' }]
      );

      // 刷新状态
      await loadCheckInStatus();
      
      // 回调通知
      if (onCheckInSuccess) {
        onCheckInSuccess(result);
      }
    } catch (error) {
      console.error('Check-in failed:', error);
      Alert.alert(
        '签到失败',
        error instanceof Error ? error.message : '签到时发生错误',
        [{ text: '确定' }]
      );
    } finally {
      setChecking(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, style]}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (!checkInStatus) {
    return null;
  }

  const { has_checked_in_today, consecutive_days, total_check_ins } = checkInStatus;

  if (compact) {
    return (
      <TouchableOpacity
        style={[
          styles.compactButton,
          has_checked_in_today && styles.compactButtonDisabled,
          style,
        ]}
        onPress={handleCheckIn}
        disabled={has_checked_in_today || checking}
      >
        {checking ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={styles.compactButtonText}>
            {has_checked_in_today ? '✓' : '签到'}
          </Text>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        style={[
          styles.checkInButton,
          has_checked_in_today && styles.checkInButtonDisabled,
        ]}
        onPress={handleCheckIn}
        disabled={has_checked_in_today || checking}
      >
        {checking ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <Text style={styles.checkInButtonText}>
            {has_checked_in_today ? '今日已签到' : '每日签到'}
          </Text>
        )}
      </TouchableOpacity>

      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          {formatConsecutiveDays(consecutive_days)}
        </Text>
        <Text style={styles.statsText}>
          {formatTotalCheckIns(total_check_ins)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  checkInButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
  checkInButtonDisabled: {
    backgroundColor: theme.colors.success,
  },
  checkInButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  compactButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 50,
    alignItems: 'center',
    justifyContent: 'center',
  },
  compactButtonDisabled: {
    backgroundColor: theme.colors.success,
  },
  compactButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  statsContainer: {
    marginTop: 12,
    alignItems: 'center',
  },
  statsText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginVertical: 2,
  },
});
