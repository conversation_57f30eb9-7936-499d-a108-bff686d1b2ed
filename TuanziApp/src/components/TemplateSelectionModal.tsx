// TuanziApp/src/components/TemplateSelectionModal.tsx

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  FlatList,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { getRoomTemplates } from '../api/calendarApi';
import { EventTemplate } from '../types';
import { theme } from '../styles/theme';

const { height: screenHeight } = Dimensions.get('window');

interface TemplateSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectTemplate: (template: EventTemplate) => void;
  selectedTemplateId?: string;
  title?: string;
}

export const TemplateSelectionModal: React.FC<TemplateSelectionModalProps> = ({
  visible,
  onClose,
  onSelectTemplate,
  selectedTemplateId,
  title = '选择房间模板',
}) => {
  const [templates, setTemplates] = useState<EventTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchTemplates = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await getRoomTemplates();
      setTemplates(data);
    } catch (error) {
      console.error('Failed to load templates:', error);
      Alert.alert('错误', '无法加载模板列表。');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (visible) {
      fetchTemplates();
    }
  }, [visible, fetchTemplates]);

  const handleSelectTemplate = (template: EventTemplate) => {
    onSelectTemplate(template);
    onClose();
  };

  const renderTemplateItem = ({ item }: { item: EventTemplate }) => {
    const isSelected = selectedTemplateId === item.id;
    
    return (
      <TouchableOpacity 
        style={[
          styles.templateCard, 
          isSelected && styles.templateCardSelected
        ]} 
        onPress={() => handleSelectTemplate(item)}
      >
        <View style={styles.templateHeader}>
          <Text style={styles.templateName}>
            {item.type === 'system' ? '🏛️' : '👤'} {item.name}
          </Text>
          <Text style={styles.templateType}>
            {item.type === 'system' ? '系统模板' : '我的模板'}
          </Text>
        </View>
        <Text style={styles.templateDescription}>{item.description}</Text>
        {item.steps && item.steps.length > 0 && (
          <Text style={styles.stepsCount}>
            包含 {item.steps.length} 个环节
          </Text>
        )}
        {isSelected && (
          <View style={styles.selectedIndicator}>
            <Text style={styles.selectedText}>✓ 已选择</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // 分离系统模板和用户模板
  const systemTemplates = templates.filter(t => t.type === 'system');
  const userTemplates = templates.filter(t => t.type === 'user');
  
  // 合并模板列表，系统模板在前
  const sortedTemplates = [...systemTemplates, ...userTemplates];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        {/* 模态框头部 */}
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>{title}</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* 内容区域 */}
        <View style={styles.modalContent}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={styles.loadingText}>加载模板...</Text>
            </View>
          ) : templates.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>暂无可用模板</Text>
            </View>
          ) : (
            <FlatList
              data={sortedTemplates}
              renderItem={renderTemplateItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.listContainer}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
    fontWeight: 'bold',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  listContainer: {
    paddingVertical: 16,
  },
  templateCard: {
    backgroundColor: theme.colors.surface,
    padding: 20,
    marginVertical: 8,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  templateCardSelected: {
    borderColor: theme.colors.primary,
    borderWidth: 2,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  templateName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    flex: 1,
  },
  templateType: {
    fontSize: 12,
    color: theme.colors.primary,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    fontWeight: 'bold',
  },
  templateDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 5,
    lineHeight: 20,
  },
  stepsCount: {
    fontSize: 12,
    color: theme.colors.primary,
    marginTop: 8,
    fontWeight: '500',
  },
  selectedIndicator: {
    marginTop: 10,
    alignSelf: 'flex-end',
  },
  selectedText: {
    color: theme.colors.primary,
    fontWeight: 'bold',
    fontSize: 12,
  },
});
