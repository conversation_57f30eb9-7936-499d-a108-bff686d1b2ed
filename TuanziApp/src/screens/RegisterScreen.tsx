import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  Alert,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Animated,
  Dimensions
} from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { useNavigation } from '@react-navigation/native';
import { API_URL } from '../api/client';
import { theme } from '../styles/theme';
export const RegisterScreen = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const navigation = useNavigation<any>();
  const { width } = Dimensions.get('window');

  // 动画值
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(width * 0.1));

  // 组件挂载时启动动画
  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      })
    ]).start();
  }, []);

  const validateForm = () => {
    if (!username.trim()) {
      Alert.alert("注册失败", "用户名不能为空");
      return false;
    }
    if (username.length < 3) {
      Alert.alert("注册失败", "用户名至少需要3个字符");
      return false;
    }
    if (!password.trim()) {
      Alert.alert("注册失败", "密码不能为空");
      return false;
    }
    if (password.length < 6) {
      Alert.alert("注册失败", "密码至少需要6个字符");
      return false;
    }
    if (password !== confirmPassword) {
      Alert.alert("注册失败", "两次输入的密码不一致");
      return false;
    }
    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/api/users/register/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });

      if (response.status === 201) {
        Alert.alert("注册成功", "现在将为您自动登录。", [
          { text: "好的", onPress: () => login(username, password) }
        ]);
      } else {
        const data = await response.json();
        const errorMessage = Object.values(data).join('\n');
        Alert.alert("注册失败", errorMessage);
      }
    } catch (error) {
      console.error(error);
      Alert.alert("错误", "注册过程中发生错误。");
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = username.trim().length >= 3 &&
                     password.length >= 6 &&
                     password === confirmPassword;

  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <Animated.View
          style={[
            styles.container,
            {
              opacity: fadeAnim,
              transform: [{ translateX: slideAnim }]
            }
          ]}
        >
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>团子</Text>
            <Text style={styles.tagline}>加入我们的团建社区</Text>
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.title}>创建账户</Text>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>用户名</Text>
              <TextInput
                style={styles.input}
                placeholder="请输入用户名（至少3个字符）"
                placeholderTextColor={theme.colors.gray500}
                value={username}
                onChangeText={setUsername}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>密码</Text>
              <TextInput
                style={styles.input}
                placeholder="请输入密码（至少6个字符）"
                placeholderTextColor={theme.colors.gray500}
                value={password}
                onChangeText={setPassword}
                secureTextEntry
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>确认密码</Text>
              <TextInput
                style={[
                  styles.input,
                  confirmPassword && password !== confirmPassword && styles.inputError
                ]}
                placeholder="请再次输入密码"
                placeholderTextColor={theme.colors.gray500}
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry
              />
              {confirmPassword && password !== confirmPassword && (
                <Text style={styles.errorText}>密码不一致</Text>
              )}
            </View>

            <TouchableOpacity
              style={[
                styles.registerButton,
                (!isFormValid || isLoading) && styles.registerButtonDisabled
              ]}
              onPress={handleRegister}
              disabled={!isFormValid || isLoading}
            >
              <Text style={styles.registerButtonText}>
                {isLoading ? "注册中..." : "创建账户"}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.loginLink}
              onPress={() => navigation.goBack()}
            >
              <Text style={styles.loginLinkText}>
                已有账户？<Text style={styles.loginLinkTextHighlight}>返回登录</Text>
              </Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: theme.spacing['2xl'],
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing['4xl'],
  },
  logoText: {
    fontSize: theme.typography.fontSize['8xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.primary,
    marginBottom: theme.spacing.sm,
  },
  tagline: {
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.fontWeight.medium,
  },
  formContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius['2xl'],
    padding: theme.spacing['2xl'],
    ...theme.shadows.lg,
  },
  title: {
    fontSize: theme.typography.fontSize['3xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    textAlign: 'center',
    marginBottom: theme.spacing['2xl'],
  },
  inputContainer: {
    marginBottom: theme.spacing.lg,
  },
  inputLabel: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  input: {
    height: 48,
    borderColor: theme.colors.border,
    borderWidth: 1,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.lg,
    fontSize: theme.typography.fontSize.lg,
    backgroundColor: theme.colors.surface,
    color: theme.colors.textPrimary,
  },
  inputError: {
    borderColor: theme.colors.error,
  },
  errorText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.error,
    marginTop: theme.spacing.xs,
  },
  registerButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.lg,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: theme.spacing.lg,
    ...theme.shadows.md,
  },
  registerButtonDisabled: {
    backgroundColor: theme.colors.gray300,
    ...theme.shadows.none,
  },
  registerButtonText: {
    color: theme.colors.white,
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
  },
  loginLink: {
    marginTop: theme.spacing['2xl'],
    alignItems: 'center',
  },
  loginLinkText: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
  },
  loginLinkTextHighlight: {
    color: theme.colors.primary,
    fontWeight: theme.typography.fontWeight.semibold,
  },
});
