// TuanziApp/src/screens/RoomScreenOptimized.tsx

import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { theme } from '../styles/theme';
import { useAuth } from '../auth/AuthContext';
import { useRoom } from '../contexts/RoomContext';

// 导入子组件
import { LobbyView } from '../components/LobbyView';
import { PictionaryView } from '../components/PictionaryView';
import { ChatView } from '../components/ChatView';

type RoomScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Room'>;
type RoomScreenRouteProp = RouteProp<RootStackParamList, 'Room'>;

interface RoomScreenOptimizedProps {}

export const RoomScreenOptimized: React.FC<RoomScreenOptimizedProps> = () => {
  const navigation = useNavigation<RoomScreenNavigationProp>();
  const route = useRoute<RoomScreenRouteProp>();
  const { user, token } = useAuth();
  
  // 使用房间Context
  const {
    room,
    setRoom,
    isConnected,
    roomStatus,
    currentStep,
    messages,
    paths,
    pictionaryState,
    sendMessage,
    sendGuess,
    sendDrawing,
    nextStep,
    returnToLobby,
    connect,
    disconnect,
  } = useRoom();

  const initialRoom = route.params?.room;

  // 初始化房间和连接
  useEffect(() => {
    if (initialRoom && token) {
      setRoom(initialRoom);
      connect(initialRoom.room_code, token);
    }

    return () => {
      disconnect();
    };
  }, [initialRoom, token, setRoom, connect, disconnect]);

  // 处理返回按钮
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (isConnected) {
        e.preventDefault();
        
        Alert.alert(
          '离开房间',
          '确定要离开房间吗？',
          [
            { text: '取消', style: 'cancel' },
            {
              text: '确定',
              style: 'destructive',
              onPress: () => {
                disconnect();
                navigation.dispatch(e.data.action);
              },
            },
          ]
        );
      }
    });

    return unsubscribe;
  }, [navigation, isConnected, disconnect]);

  // 辅助函数：判断当前用户是否为房主
  const isCurrentUserHost = () => {
    if (!user?.username || !initialRoom) return false;

    // 首先检查房间的host字段
    if (initialRoom.host === user.username) return true;

    // 然后检查participants数组中的角色信息
    const userParticipant = initialRoom.participants?.find(
      p => p.username === user.username
    );
    return userParticipant?.role === 'host';
  };

  // 渲染当前步骤
  const renderCurrentStep = () => {
    if (roomStatus === 'FINISHED') {
      return <LobbyView isFinished={true} />;
    }

    if (!currentStep || roomStatus === 'WAITING' || roomStatus === 'READY') {
      const isHost = isCurrentUserHost();
      return (
        <LobbyView
          isHost={isHost}
          onNextStep={nextStep}
          isConnected={isConnected}
        />
      );
    }

    switch (currentStep.step_type) {
      case 'DRAWING_GAME':
        if (!pictionaryState) {
          return <View style={styles.loadingContainer}><Text>Loading...</Text></View>;
        }
        return (
          <PictionaryView
            isDrawer={user?.username === pictionaryState.drawer}
            pictionaryState={pictionaryState}
            paths={paths}
            messages={messages}
            onDraw={sendDrawing}
            onSendMessage={sendMessage}
            onSendGuess={sendGuess}
          />
        );
      case 'FREE_CHAT':
        return (
          <ChatView
            messages={messages}
            onSendMessage={sendMessage}
            currentUser={user?.username || ''}
          />
        );
      default:
        return (
          <View style={styles.unknownStep}>
            <Text style={styles.unknownStepText}>
              未知的步骤类型: {currentStep.step_type}
            </Text>
          </View>
        );
    }
  };

  // 加载状态
  if (!room) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>正在加载房间...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* 房间信息头部 */}
      <View style={styles.header}>
        <View style={styles.roomInfo}>
          <Text style={styles.roomName}>{room.name}</Text>
          <Text style={styles.roomCode}>房间代码: {room.room_code}</Text>
        </View>
        <View style={styles.connectionStatus}>
          <View 
            style={[
              styles.statusIndicator, 
              { backgroundColor: isConnected ? '#4CAF50' : '#F44336' }
            ]} 
          />
          <Text style={styles.statusText}>
            {isConnected ? '已连接' : '连接中...'}
          </Text>
        </View>
      </View>

      {/* 当前步骤内容 */}
      <View style={styles.content}>
        {renderCurrentStep()}
      </View>

      {/* 聊天区域 - 在非聊天步骤时显示 */}
      {currentStep?.step_type !== 'FREE_CHAT' && (
        <View style={styles.chatContainer}>
          <ChatView
            messages={messages}
            onSendMessage={sendMessage}
            currentUser={user?.username || ''}
            compact={true}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  roomInfo: {
    flex: 1,
  },
  roomName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  roomCode: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  content: {
    flex: 1,
  },
  chatContainer: {
    height: 200,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  unknownStep: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  unknownStepText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
});
