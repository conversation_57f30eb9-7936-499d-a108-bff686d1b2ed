/**
 * 团子APP日历页面组件
 *
 * 这是用户查看和管理预约的主要界面。
 * 按照新的设计规范，提供清晰的日历视图和预约管理功能。
 *
 * 主要功能：
 * 1. 月历视图：显示当前月份的日历
 * 2. 预约显示：在日历上标记已预约的日期
 * 3. 新建预约：顶部醒目的新建预约按钮
 * 4. 预约列表：显示当月的所有预约
 * 5. 签到功能：集成每日签到功能
 *
 * 界面特性：
 * - 顶部新建预约按钮
 * - 清晰的月历视图
 * - 预约状态的视觉标识
 * - 现代化的卡片式设计
 * - 流畅的动画效果
 * - 响应式布局适配不同屏幕
 *
 * 技术实现：
 * - 基于React Native的函数式组件
 * - 集成预约上下文管理
 * - 使用ScrollView支持滚动
 * - 支持下拉刷新
 * - 完整的错误处理和用户反馈
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList, Reservation } from '../types';
import { theme } from '../styles/theme';
import { ReservationDetailModal } from '../components/ReservationDetailModal';
import { CheckInButton } from '../components/CheckInButton';
import { useCalendarData, useReservationDetail } from '../contexts/ReservationContext';
import { isReservationExpired } from '../api/calendarApi';
import { getCheckInStatus, hasCheckedInOnDate } from '../api/checkInApi';
import { Card, Screen } from '../components';

type CalendarScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MainTabs'>;

interface CalendarScreenProps {}

export const CalendarScreen: React.FC<CalendarScreenProps> = () => {
  const navigation = useNavigation<CalendarScreenNavigationProp>();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [refreshing, setRefreshing] = useState(false);
  const [showReservationDetail, setShowReservationDetail] = useState(false);
  const [checkInDates, setCheckInDates] = useState<string[]>([]);

  // 使用Context管理日历数据和预约详情
  const { calendarData, loading, error, loadCalendarData, refreshCalendarData } = useCalendarData();
  const { selectedReservation, selectReservation } = useReservationDetail();

  // 加载签到数据
  const loadCheckInData = useCallback(async () => {
    try {
      const checkInStatus = await getCheckInStatus();
      setCheckInDates(checkInStatus.month_check_in_dates);
    } catch (error) {
      console.error('Failed to load check-in data:', error);
      // 静默失败，不影响日历显示
    }
  }, []);

  // 当月份变化时加载数据
  useEffect(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    loadCalendarData(year, month);
    loadCheckInData();
  }, [currentDate, loadCalendarData, loadCheckInData]);

  // 页面聚焦时重新加载数据（仅在首次聚焦时）
  useFocusEffect(
    useCallback(() => {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;
      loadCalendarData(year, month);
    }, [loadCalendarData]) // 移除currentDate依赖，避免重复请求
  );

  // 下拉刷新
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await refreshCalendarData();
    setRefreshing(false);
  }, [refreshCalendarData]);

  // 切换月份
  const changeMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  // 处理日期点击
  const handleDatePress = (date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    navigation.navigate('ScheduleRoom', { selectedDate: dateString });
  };

  // 处理预约点击
  const handleReservationPress = (reservation: Reservation) => {
    selectReservation(reservation);
    setShowReservationDetail(true);
  };

  // 处理预约更新（取消等操作后）
  const handleReservationUpdated = () => {
    refreshCalendarData(); // 重新加载数据
  };

  // 渲染月份导航
  const renderMonthNavigation = () => {
    const monthNames = [
      '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];
    
    return (
      <View style={styles.monthNavigation}>
        <TouchableOpacity 
          style={styles.navButton} 
          onPress={() => changeMonth('prev')}
        >
          <Text style={styles.navButtonText}>‹</Text>
        </TouchableOpacity>
        
        <Text style={styles.monthTitle}>
          {currentDate.getFullYear()}年 {monthNames[currentDate.getMonth()]}
        </Text>
        
        <TouchableOpacity 
          style={styles.navButton} 
          onPress={() => changeMonth('next')}
        >
          <Text style={styles.navButtonText}>›</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // 渲染日历网格
  const renderCalendarGrid = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    // 获取当月第一天是星期几 (0=周日, 1=周一, ...)
    const firstDayOfWeek = firstDay.getDay();
    
    // 生成日历网格
    const days = [];
    
    // 添加上个月的日期填充
    for (let i = 0; i < firstDayOfWeek; i++) {
      const prevDate = new Date(year, month, -firstDayOfWeek + i + 1);
      days.push({ date: prevDate, isCurrentMonth: false });
    }
    
    // 添加当月的日期
    for (let day = 1; day <= lastDay.getDate(); day++) {
      const date = new Date(year, month, day);
      days.push({ date, isCurrentMonth: true });
    }
    
    // 添加下个月的日期填充 (确保总共42个格子，6行7列)
    const remainingDays = 42 - days.length;
    for (let day = 1; day <= remainingDays; day++) {
      const nextDate = new Date(year, month + 1, day);
      days.push({ date: nextDate, isCurrentMonth: false });
    }

    return (
      <View style={styles.calendarGrid}>
        {/* 星期标题 */}
        <View style={styles.weekHeader}>
          {['日', '一', '二', '三', '四', '五', '六'].map((day) => (
            <Text key={day} style={styles.weekHeaderText}>{day}</Text>
          ))}
        </View>
        
        {/* 日期网格 */}
        <View style={styles.daysGrid}>
          {days.map((dayInfo, index) => {
            const { date, isCurrentMonth } = dayInfo;
            const dateString = date.toISOString().split('T')[0];
            
            // 查找该日期的预约
            const dayReservations = calendarData?.reservations.filter(reservation => {
              const reservationDate = new Date(reservation.start_time).toISOString().split('T')[0];
              return reservationDate === dateString;
            }) || [];

            // 检查预约状态
            const hasReservations = dayReservations.length > 0;
            const hasActiveReservations = dayReservations.some(reservation => {
              const endTime = new Date(reservation.end_time);
              return endTime >= new Date();
            });
            const hasOnlyExpiredReservations = hasReservations && !hasActiveReservations;

            // 检查签到状态
            const hasCheckedIn = hasCheckedInOnDate(checkInDates, dateString);

            const isToday = dateString === new Date().toISOString().split('T')[0];
            const isPast = date < new Date(new Date().toISOString().split('T')[0]);
            
            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.dayCell,
                  !isCurrentMonth && styles.dayCell_otherMonth,
                  isToday && styles.dayCell_today,
                  hasActiveReservations && styles.dayCell_hasReservations,
                  hasOnlyExpiredReservations && styles.dayCell_hasExpiredReservations,
                  isPast && styles.dayCell_past,
                ]}
                onPress={() => isCurrentMonth && !isPast && handleDatePress(date)}
                disabled={!isCurrentMonth || isPast}
              >
                <Text style={[
                  styles.dayText,
                  !isCurrentMonth && styles.dayText_otherMonth,
                  isToday && styles.dayText_today,
                  isPast && styles.dayText_past,
                ]}>
                  {date.getDate()}
                </Text>
                {hasReservations && (
                  <View style={styles.reservationIndicator}>
                    <Text style={styles.reservationCount}>{dayReservations.length}</Text>
                  </View>
                )}
                {hasCheckedIn && (
                  <View style={styles.checkInIndicator}>
                    <Text style={styles.checkInMark}>✓</Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  // 渲染预约列表
  const renderReservationsList = () => {
    // 过滤掉已过期的预约
    const activeReservations = calendarData?.reservations.filter(reservation => {
      const endTime = new Date(reservation.end_time);
      return endTime >= new Date();
    }) || [];

    if (!activeReservations.length) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyStateText}>本月暂无有效预约</Text>
        </View>
      );
    }

    return (
      <View style={styles.reservationsList}>
        <Text style={styles.sectionTitle}>本月预约</Text>
        {activeReservations.map((reservation) => (
          <ReservationItem
            key={reservation.id}
            reservation={reservation}
            onPress={() => handleReservationPress(reservation)}
          />
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={styles.loadingText}>加载日历数据...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>加载日历数据失败</Text>
        <Text style={styles.errorDetails}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => loadCalendarData(currentDate.getFullYear(), currentDate.getMonth() + 1)}
        >
          <Text style={styles.retryButtonText}>重试</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <Screen style={styles.container}>
      {/* 顶部新建预约按钮 */}
      <View style={styles.headerSection}>
        <Card style={styles.createReservationCard} variant="vibrant">
          <TouchableOpacity
            style={styles.createReservationButton}
            onPress={() => navigation.navigate('ScheduleRoom', {})}
          >
            <View style={styles.createReservationContent}>
              <Text style={styles.createReservationIcon}>📅</Text>
              <Text style={styles.createReservationText}>新建预约</Text>
            </View>
          </TouchableOpacity>
        </Card>
      </View>

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* 月份导航 */}
        {renderMonthNavigation()}

        {/* 日历网格 */}
        {renderCalendarGrid()}

        {/* 预约列表 */}
        {renderReservationsList()}

        {/* 签到按钮 */}
        <View style={styles.checkInSection}>
          <CheckInButton
            onCheckInSuccess={loadCheckInData}
            style={styles.checkInContainer}
          />
        </View>
      </ScrollView>

      {/* 预约详情模态框 */}
      <ReservationDetailModal
        visible={showReservationDetail}
        onClose={() => setShowReservationDetail(false)}
        reservation={selectedReservation}
        onReservationUpdated={handleReservationUpdated}
      />
    </Screen>
  );
};

// 预约项组件
const ReservationItem: React.FC<{
  reservation: Reservation;
  onPress?: () => void;
}> = ({ reservation, onPress }) => {
  const startTime = new Date(reservation.start_time);
  const endTime = new Date(reservation.end_time);

  // 格式化时间显示，包含完整的年月日时分信息
  const formatDateTime = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}年${month}月${day}日 ${hours}:${minutes}`;
  };

  // 检查是否跨天
  const isCrossDay = startTime.toDateString() !== endTime.toDateString();

  // 检查是否已过期
  const isExpired = isReservationExpired(endTime);

  return (
    <TouchableOpacity
      style={[
        styles.reservationItem,
        isExpired && styles.reservationItem_expired
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.reservationHeader}>
        <View style={styles.reservationTitleContainer}>
          <Text style={[
            styles.reservationName,
            isExpired && styles.reservationText_expired
          ]}>
            {reservation.name}
            {isExpired && ' (已过期)'}
          </Text>
          <Text style={[
            styles.reservationRoomCode,
            isExpired && styles.reservationText_expired
          ]}>
            房间号: {reservation.room_code}
          </Text>
        </View>
        <Text style={[
          styles.reservationHost,
          isExpired && styles.reservationText_expired
        ]}>
          房主: {reservation.host_username}
        </Text>
      </View>
      <View style={styles.reservationTimeContainer}>
        <Text style={[
          styles.reservationTimeLabel,
          isExpired && styles.reservationText_expired
        ]}>
          开始时间:
        </Text>
        <Text style={[
          styles.reservationTime,
          isExpired && styles.reservationText_expired
        ]}>
          {formatDateTime(startTime)}
        </Text>
      </View>
      <View style={styles.reservationTimeContainer}>
        <Text style={[
          styles.reservationTimeLabel,
          isExpired && styles.reservationText_expired
        ]}>
          结束时间:
        </Text>
        <Text style={[
          styles.reservationTime,
          isExpired && styles.reservationText_expired
        ]}>
          {isCrossDay ? formatDateTime(endTime) : `${String(endTime.getHours()).padStart(2, '0')}:${String(endTime.getMinutes()).padStart(2, '0')}`}
        </Text>
      </View>
      <Text style={[
        styles.reservationParticipants,
        isExpired && styles.reservationText_expired
      ]}>
        参与者: {reservation.current_participants}/{reservation.max_participants}
      </Text>
      {isCrossDay && (
        <Text style={styles.crossDayIndicator}>⚠️ 跨天预约</Text>
      )}
      {/* 添加点击提示 */}
      <Text style={styles.tapHint}>点击查看详情</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  // 头部区域
  headerSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
  },

  // 新建预约卡片
  createReservationCard: {
    backgroundColor: theme.colors.primary + '15',
    borderColor: theme.colors.primary + '30',
    borderWidth: 2,
  },
  createReservationButton: {
    padding: theme.spacing.lg,
  },
  createReservationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.md,
  },
  createReservationIcon: {
    fontSize: 28,
  },
  createReservationText: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.primary,
  },

  // 滚动容器
  scrollContainer: {
    flex: 1,
  },

  // 签到区域
  checkInSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.xl,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.error || '#ff0000',
    marginBottom: 8,
    textAlign: 'center',
  },
  errorDetails: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 20,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: theme.colors.surface,
    fontSize: 16,
    fontWeight: 'bold',
  },
  monthNavigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  navButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    backgroundColor: theme.colors.primary,
  },
  navButtonText: {
    fontSize: 24,
    color: theme.colors.surface,
    fontWeight: 'bold',
  },
  monthTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
  },
  calendarGrid: {
    backgroundColor: theme.colors.surface,
    margin: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  weekHeader: {
    flexDirection: 'row',
    backgroundColor: theme.colors.primary,
  },
  weekHeaderText: {
    flex: 1,
    textAlign: 'center',
    paddingVertical: 12,
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  dayCell: {
    width: '14.28%', // 100% / 7 days
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: theme.colors.border,
    position: 'relative',
  },
  dayCell_otherMonth: {
    backgroundColor: theme.colors.gray200,
  },
  dayCell_today: {
    backgroundColor: theme.colors.primaryLight,
  },
  dayCell_hasReservations: {
    backgroundColor: theme.colors.success + '20',
  },
  dayCell_hasExpiredReservations: {
    backgroundColor: theme.colors.gray300 || '#d0d0d0',
  },
  dayCell_past: {
    backgroundColor: theme.colors.gray200,
  },
  dayText: {
    fontSize: 16,
    color: theme.colors.textPrimary,
  },
  dayText_otherMonth: {
    color: theme.colors.textSecondary,
  },
  dayText_today: {
    color: theme.colors.surface,
    fontWeight: 'bold',
  },
  dayText_past: {
    color: theme.colors.textSecondary,
  },
  reservationIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  reservationCount: {
    fontSize: 10,
    color: theme.colors.surface,
    fontWeight: 'bold',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 12,
  },
  reservationsList: {
    padding: 16,
  },
  emptyState: {
    padding: 32,
    alignItems: 'center',
  },
  emptyStateText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  reservationItem: {
    backgroundColor: theme.colors.surface,
    padding: 16,
    marginBottom: 8,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  reservationItem_expired: {
    backgroundColor: theme.colors.gray100 || '#f5f5f5',
    borderLeftColor: theme.colors.gray400 || '#999999',
    opacity: 0.7,
  },
  reservationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  reservationTitleContainer: {
    flex: 1,
    marginRight: 8,
  },
  reservationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.textPrimary,
    marginBottom: 2,
  },
  reservationRoomCode: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  reservationHost: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  reservationTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  reservationTimeLabel: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    width: 60,
    marginRight: 8,
  },
  reservationTime: {
    fontSize: 14,
    color: theme.colors.textPrimary,
    flex: 1,
  },
  reservationParticipants: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 4,
  },
  crossDayIndicator: {
    fontSize: 12,
    color: theme.colors.warning || '#ff9500',
    fontWeight: 'bold',
    marginTop: 4,
  },
  checkInIndicator: {
    position: 'absolute',
    top: 2,
    left: 2,
    backgroundColor: theme.colors.success,
    borderRadius: 8,
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkInMark: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  checkInContainer: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    margin: 16,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.surface,
  },
  tapHint: {
    fontSize: 12,
    color: theme.colors.primary,
    textAlign: 'right',
    marginTop: 8,
    fontStyle: 'italic',
  },
  reservationText_expired: {
    color: theme.colors.gray500 || '#666666',
    textDecorationLine: 'line-through',
  },
});
