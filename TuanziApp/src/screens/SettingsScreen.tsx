/**
 * 团子APP设置页面组件
 *
 * 这是用户管理个人信息和应用设置的主要界面。
 * 按照新的设计规范，提供清晰的设置管理体验。
 *
 * 主要功能：
 * 1. 用户信息：显示和编辑用户基本信息
 * 2. 头像设置：上传和更换用户头像
 * 3. 邮箱管理：绑定和修改邮箱地址
 * 4. 订阅管理：查看和管理订阅状态
 * 5. 应用设置：通知、隐私等设置
 * 6. 账户操作：退出登录、注销账户等
 *
 * 界面特性：
 * - 清晰的分组设置项
 * - 现代化的卡片式设计
 * - 直观的操作反馈
 * - 流畅的动画效果
 * - 响应式布局适配不同屏幕
 *
 * 技术实现：
 * - 基于React Native的函数式组件
 * - 集成认证和订阅上下文
 * - 使用ScrollView支持滚动
 * - 完整的错误处理和用户反馈
 * - 支持头像上传和图片选择
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Animated,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../auth/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { RootStackParamList } from '../types';
import { Card, Button, Badge, Screen } from '../components';
import { theme } from '../styles/theme';

type SettingsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MainTabs'>;

interface SettingsScreenProps {}

export const SettingsScreen: React.FC<SettingsScreenProps> = () => {
  const { user, logout } = useAuth();
  const { subscriptionInfo } = useSubscription();
  const navigation = useNavigation<SettingsScreenNavigationProp>();

  // 状态管理
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [vibrationEnabled, setVibrationEnabled] = useState(true);

  // 动画值
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(30);

  React.useEffect(() => {
    // 页面加载动画
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleLogout = useCallback(() => {
    Alert.alert(
      '确认退出',
      '您确定要退出登录吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '退出',
          style: 'destructive',
          onPress: () => {
            logout();
          },
        },
      ]
    );
  }, [logout]);

  const handleEditProfile = () => {
    // TODO: 导航到编辑个人资料页面
    Alert.alert('提示', '编辑个人资料功能即将上线');
  };

  const handleChangeAvatar = () => {
    // TODO: 实现头像更换功能
    Alert.alert('提示', '更换头像功能即将上线');
  };

  const handleManageSubscription = () => {
    navigation.navigate('Subscription');
  };

  const handleAbout = () => {
    // TODO: 导航到关于页面
    Alert.alert('关于团子', '团子APP v1.0.0\n让团建更有趣！');
  };

  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightComponent?: React.ReactNode,
    showArrow: boolean = true
  ) => (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingItemLeft}>
        <Text style={styles.settingIcon}>{icon}</Text>
        <View style={styles.settingTextContainer}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      <View style={styles.settingItemRight}>
        {rightComponent}
        {showArrow && onPress && (
          <Text style={styles.settingArrow}>›</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <Screen style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* 用户信息卡片 */}
        <Animated.View
          style={[
            styles.userSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <Card style={styles.userCard} variant="elevated">
            <View style={styles.userInfo}>
              <TouchableOpacity
                style={styles.avatarContainer}
                onPress={handleChangeAvatar}
              >
                <View style={styles.avatar}>
                  <Text style={styles.avatarText}>
                    {user?.username?.charAt(0)?.toUpperCase() || '用'}
                  </Text>
                </View>
                <View style={styles.avatarEditIcon}>
                  <Text style={styles.avatarEditText}>✏️</Text>
                </View>
              </TouchableOpacity>
              <View style={styles.userDetails}>
                <Text style={styles.userName}>{user?.username || '用户'}</Text>
                <Text style={styles.userEmail}>{user?.email || '未绑定邮箱'}</Text>
                <Badge
                  text={subscriptionInfo?.current_level || 'Free'}
                  variant={subscriptionInfo?.current_level === 'Free' ? 'neutral' : 'primary'}
                  size="small"
                />
              </View>
              <TouchableOpacity
                style={styles.editButton}
                onPress={handleEditProfile}
              >
                <Text style={styles.editButtonText}>编辑</Text>
              </TouchableOpacity>
            </View>
          </Card>
        </Animated.View>

        {/* 账户设置 */}
        <Animated.View
          style={[
            styles.section,
            { opacity: fadeAnim }
          ]}
        >
          <Text style={styles.sectionTitle}>账户设置</Text>
          <Card style={styles.settingsCard} variant="filled">
            {renderSettingItem(
              '👤',
              '个人资料',
              '编辑用户名、头像等信息',
              handleEditProfile
            )}
            {renderSettingItem(
              '📧',
              '邮箱管理',
              user?.email || '未绑定邮箱',
              () => Alert.alert('提示', '邮箱管理功能即将上线')
            )}
            {renderSettingItem(
              '💎',
              '订阅管理',
              `当前：${subscriptionInfo?.current_level || 'Free'}`,
              handleManageSubscription
            )}
          </Card>
        </Animated.View>

        {/* 应用设置 */}
        <Animated.View
          style={[
            styles.section,
            { opacity: fadeAnim }
          ]}
        >
          <Text style={styles.sectionTitle}>应用设置</Text>
          <Card style={styles.settingsCard} variant="filled">
            {renderSettingItem(
              '🔔',
              '推送通知',
              '接收重要消息提醒',
              undefined,
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
                trackColor={{ false: theme.colors.border, true: theme.colors.primary + '50' }}
                thumbColor={notificationsEnabled ? theme.colors.primary : theme.colors.textTertiary}
              />,
              false
            )}
            {renderSettingItem(
              '🔊',
              '声音',
              '消息提示音',
              undefined,
              <Switch
                value={soundEnabled}
                onValueChange={setSoundEnabled}
                trackColor={{ false: theme.colors.border, true: theme.colors.primary + '50' }}
                thumbColor={soundEnabled ? theme.colors.primary : theme.colors.textTertiary}
              />,
              false
            )}
            {renderSettingItem(
              '📳',
              '震动',
              '触觉反馈',
              undefined,
              <Switch
                value={vibrationEnabled}
                onValueChange={setVibrationEnabled}
                trackColor={{ false: theme.colors.border, true: theme.colors.primary + '50' }}
                thumbColor={vibrationEnabled ? theme.colors.primary : theme.colors.textTertiary}
              />,
              false
            )}
          </Card>
        </Animated.View>

        {/* 其他设置 */}
        <Animated.View
          style={[
            styles.section,
            { opacity: fadeAnim }
          ]}
        >
          <Text style={styles.sectionTitle}>其他</Text>
          <Card style={styles.settingsCard} variant="filled">
            {renderSettingItem(
              '❓',
              '帮助与反馈',
              '使用指南、问题反馈',
              () => Alert.alert('提示', '帮助功能即将上线')
            )}
            {renderSettingItem(
              'ℹ️',
              '关于团子',
              '版本信息、开发团队',
              handleAbout
            )}
            {renderSettingItem(
              '🔒',
              '隐私政策',
              '了解我们如何保护您的隐私',
              () => Alert.alert('提示', '隐私政策功能即将上线')
            )}
          </Card>
        </Animated.View>

        {/* 退出登录按钮 */}
        <Animated.View
          style={[
            styles.logoutSection,
            { opacity: fadeAnim }
          ]}
        >
          <Button
            title="退出登录"
            onPress={handleLogout}
            variant="outline"
            size="large"
            style={styles.logoutButton}
          />
        </Animated.View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: theme.spacing['2xl'],
  },

  // 用户信息区域
  userSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
  },
  userCard: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.md,
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.primary + '50',
  },
  avatarText: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.primary,
  },
  avatarEditIcon: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  avatarEditText: {
    fontSize: 10,
  },
  userDetails: {
    flex: 1,
    gap: theme.spacing.xs,
  },
  userName: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  userEmail: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  editButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.surface,
  },
  editButtonText: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.primary,
    fontWeight: theme.typography.fontWeight.medium,
  },

  // 设置区域
  section: {
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.md,
  },
  settingsCard: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },

  // 设置项
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: theme.spacing.md,
  },
  settingIcon: {
    fontSize: 20,
    width: 24,
    textAlign: 'center',
  },
  settingTextContainer: {
    flex: 1,
    gap: theme.spacing.xs,
  },
  settingTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.textPrimary,
  },
  settingSubtitle: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
  },
  settingItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  settingArrow: {
    fontSize: theme.typography.fontSize.xl,
    color: theme.colors.textTertiary,
    fontWeight: theme.typography.fontWeight.bold,
  },

  // 退出登录区域
  logoutSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing.xl,
  },
  logoutButton: {
    borderColor: theme.colors.error || '#ff0000',
  },
});
