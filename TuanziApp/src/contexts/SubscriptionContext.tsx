/**
 * 订阅管理上下文 - 管理用户订阅状态和权限
 *
 * 功能：
 * - 获取和缓存用户订阅信息
 * - 处理订阅等级升级/降级
 * - 提供权限检查功能
 * - 防抖机制避免频繁API调用
 *
 * 使用方式：
 * 1. 在App根组件包裹SubscriptionProvider
 * 2. 在子组件中使用useSubscription()获取订阅状态
 * 3. 使用useSubscriptionPermission()检查权限
 */

import React, { createContext, useContext, useState, useEffect, ReactNode, useRef, useCallback } from 'react';
import { useAuth } from '../auth/AuthContext';
import {
  getSubscriptionInfo,
  updateSubscriptionLevel,
  SubscriptionInfo,
  getSubscriptionPlans,
  checkSubscriptionPermission
} from '../api/subscriptionApi';

// 订阅等级类型定义
type SubscriptionLevel = 'Free' | 'Pro' | 'Max';

/**
 * 订阅上下文接口定义
 */
interface SubscriptionContextType {
  subscriptionInfo: SubscriptionInfo | null;  // 当前用户订阅信息
  isLoading: boolean;                         // 是否正在加载
  error: string | null;                       // 错误信息
  refreshSubscriptionInfo: () => Promise<void>;  // 刷新订阅信息
  upgradeSubscription: (targetLevel: SubscriptionLevel, isDebug?: boolean) => Promise<boolean>;  // 升级订阅
  checkPermission: (requiredLevel: SubscriptionLevel) => boolean;  // 检查权限
  getPlans: () => ReturnType<typeof getSubscriptionPlans>;  // 获取订阅计划
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

/**
 * 订阅上下文提供者组件
 * 管理全局订阅状态，提供订阅相关的所有功能
 */
export const SubscriptionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, token, updateToken } = useAuth();  // 获取用户认证信息

  // 订阅状态管理
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 防抖机制：避免频繁调用API，提高性能和用户体验
  const refreshTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const lastRefreshTimeRef = useRef<number>(0);
  const REFRESH_DEBOUNCE_MS = 1000; // 1秒防抖延迟
  const MIN_REFRESH_INTERVAL_MS = 5000; // 最小刷新间隔5秒，避免过度请求

  /**
   * 实际执行API调用的函数
   * 直接调用后端API获取最新的订阅信息，不经过防抖
   */
  const performRefresh = useCallback(async () => {
    if (!token) return;

    setIsLoading(true);
    setError(null);

    try {
      const info = await getSubscriptionInfo(token);
      setSubscriptionInfo(info);
      lastRefreshTimeRef.current = Date.now();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load subscription info');
      console.error('Failed to refresh subscription info:', err);
    } finally {
      setIsLoading(false);
    }
  }, [token]);

  /**
   * 带防抖的刷新函数
   * 避免频繁调用API，提高性能和减少服务器负载
   *
   * 防抖策略：
   * 1. 如果距离上次刷新时间小于最小间隔，直接跳过
   * 2. 使用定时器延迟执行，如果在延迟期间再次调用，会取消之前的定时器
   */
  const refreshSubscriptionInfo = useCallback(async () => {
    if (!token) return;

    const now = Date.now();
    const timeSinceLastRefresh = now - lastRefreshTimeRef.current;

    // 如果距离上次刷新时间太短，则跳过
    if (timeSinceLastRefresh < MIN_REFRESH_INTERVAL_MS) {
      console.log('Subscription refresh skipped: too frequent');
      return;
    }

    // 清除之前的防抖定时器
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // 设置新的防抖定时器
    refreshTimeoutRef.current = setTimeout(() => {
      performRefresh();
    }, REFRESH_DEBOUNCE_MS);
  }, [token, performRefresh, MIN_REFRESH_INTERVAL_MS, REFRESH_DEBOUNCE_MS]);

  const upgradeSubscription = async (
    targetLevel: SubscriptionLevel, 
    isDebug: boolean = false
  ): Promise<boolean> => {
    if (!token) {
      setError('No authentication token available');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await updateSubscriptionLevel(token, targetLevel, isDebug);
      
      if (response.new_level && response.access_token) {
        // 更新AuthContext中的token和用户信息
        await updateToken(response.access_token);

        // 等待一小段时间确保token更新完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 订阅更新成功后，立即刷新订阅信息（绕过防抖）
        await performRefresh();

        // 如果是调试模式且成功，记录日志
        if (isDebug && user) {
          console.log('Debug mode: Subscription level updated to', targetLevel);
        }

        return true;
      } else if (response.payment_url) {
        // 如果返回了支付URL，应该跳转到支付页面
        console.log('Payment URL:', response.payment_url);
        // TODO: 处理支付流程
        return false;
      } else {
        setError(response.message || 'Subscription update failed');
        return false;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upgrade subscription');
      console.error('Failed to upgrade subscription:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const checkPermission = (requiredLevel: SubscriptionLevel): boolean => {
    if (!subscriptionInfo) return false;
    return checkSubscriptionPermission(subscriptionInfo.current_level, requiredLevel);
  };

  const getPlans = () => {
    return getSubscriptionPlans();
  };

  // 当用户登录状态改变时，刷新订阅信息
  useEffect(() => {
    if (user && token) {
      refreshSubscriptionInfo();
    } else {
      setSubscriptionInfo(null);
      setError(null);
    }
  }, [user, token, refreshSubscriptionInfo]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  const value: SubscriptionContextType = {
    subscriptionInfo,
    isLoading,
    error,
    refreshSubscriptionInfo,
    upgradeSubscription,
    checkPermission,
    getPlans,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

// 便捷的权限检查Hook
export const useSubscriptionPermission = (requiredLevel: SubscriptionLevel) => {
  const { checkPermission } = useSubscription();
  return checkPermission(requiredLevel);
};

// 便捷的订阅等级Hook
export const useSubscriptionLevel = () => {
  const { subscriptionInfo } = useSubscription();
  return subscriptionInfo?.current_level || 'Free';
};
