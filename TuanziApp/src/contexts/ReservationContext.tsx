// TuanziApp/src/contexts/ReservationContext.tsx

import React, { createContext, useContext, useState, useCallback, ReactNode, useRef } from 'react';
import { EventTemplate, CalendarData, Reservation } from '../types';
import { getMonthCalendarData, handleApiError } from '../api/calendarApi';

/**
 * 预约管理上下文
 * 提供预约相关的状态管理和数据操作功能
 */

interface ReservationContextType {
  // 模板选择状态
  selectedTemplate: EventTemplate | null;
  setSelectedTemplate: (template: EventTemplate | null) => void;
  
  // 日历数据状态
  calendarData: CalendarData | null;
  calendarLoading: boolean;
  calendarError: string | null;
  
  // 预约表单状态
  reservationForm: {
    roomName: string;
    selectedDate: Date | null;
    scheduledDateTime: Date;
    durationHours: number;
  };
  updateReservationForm: (updates: Partial<ReservationContextType['reservationForm']>) => void;
  resetReservationForm: () => void;
  
  // 数据操作方法
  loadCalendarData: (year: number, month: number) => Promise<void>;
  refreshCalendarData: () => Promise<void>;
  
  // 预约详情状态
  selectedReservation: Reservation | null;
  setSelectedReservation: (reservation: Reservation | null) => void;
}

const ReservationContext = createContext<ReservationContextType | undefined>(undefined);

interface ReservationProviderProps {
  children: ReactNode;
}

const defaultReservationForm = {
  roomName: '',
  selectedDate: null,
  scheduledDateTime: new Date(),
  durationHours: 2,
};

export const ReservationProvider: React.FC<ReservationProviderProps> = ({ children }) => {
  // 模板选择状态
  const [selectedTemplate, setSelectedTemplate] = useState<EventTemplate | null>(null);

  // 日历数据状态
  const [calendarData, setCalendarData] = useState<CalendarData | null>(null);
  const [calendarLoading, setCalendarLoading] = useState(false);
  const [calendarError, setCalendarError] = useState<string | null>(null);
  const [currentCalendarDate, setCurrentCalendarDate] = useState({ year: 0, month: 0 });

  // 预约表单状态
  const [reservationForm, setReservationForm] = useState(defaultReservationForm);

  // 预约详情状态
  const [selectedReservation, setSelectedReservation] = useState<Reservation | null>(null);

  // 使用ref来跟踪当前状态，避免闭包问题
  const calendarStateRef = useRef({
    data: calendarData,
    loading: calendarLoading,
    currentDate: currentCalendarDate,
  });

  // 更新ref
  calendarStateRef.current = {
    data: calendarData,
    loading: calendarLoading,
    currentDate: currentCalendarDate,
  };

  // 更新预约表单
  const updateReservationForm = useCallback((updates: Partial<typeof defaultReservationForm>) => {
    setReservationForm(prev => ({ ...prev, ...updates }));
  }, []);

  // 重置预约表单
  const resetReservationForm = useCallback(() => {
    setReservationForm(defaultReservationForm);
    setSelectedTemplate(null);
  }, []);

  // 加载日历数据
  const loadCalendarData = useCallback(async (year: number, month: number) => {
    const currentState = calendarStateRef.current;

    // 如果是相同的年月，且已有数据，且不在加载中，则不重复加载
    if (currentState.currentDate.year === year &&
        currentState.currentDate.month === month &&
        currentState.data &&
        !currentState.loading) {
      console.log('Calendar data already loaded for', year, month, 'skipping request');
      return;
    }

    // 如果正在加载相同的数据，则不重复请求
    if (currentState.loading &&
        currentState.currentDate.year === year &&
        currentState.currentDate.month === month) {
      console.log('Calendar data already loading for', year, month, 'skipping request');
      return;
    }

    try {
      console.log('Loading calendar data for', year, month);
      setCalendarLoading(true);
      setCalendarError(null);
      setCurrentCalendarDate({ year, month });

      const data = await getMonthCalendarData(year, month);
      setCalendarData(data);
    } catch (error) {
      console.error('Failed to load calendar data:', error);
      setCalendarError(handleApiError(error));
      // 重置日期状态，允许重试
      setCurrentCalendarDate({ year: 0, month: 0 });
    } finally {
      setCalendarLoading(false);
    }
  }, []); // 空依赖数组，函数只创建一次

  // 刷新当前日历数据
  const refreshCalendarData = useCallback(async () => {
    const currentState = calendarStateRef.current;
    if (currentState.currentDate.year && currentState.currentDate.month) {
      console.log('Refreshing calendar data for', currentState.currentDate.year, currentState.currentDate.month);
      // 强制重新加载，清除缓存
      setCurrentCalendarDate({ year: 0, month: 0 });
      setCalendarData(null);
      await loadCalendarData(currentState.currentDate.year, currentState.currentDate.month);
    }
  }, [loadCalendarData]);

  const contextValue: ReservationContextType = {
    // 模板选择
    selectedTemplate,
    setSelectedTemplate,
    
    // 日历数据
    calendarData,
    calendarLoading,
    calendarError,
    
    // 预约表单
    reservationForm,
    updateReservationForm,
    resetReservationForm,
    
    // 数据操作
    loadCalendarData,
    refreshCalendarData,
    
    // 预约详情
    selectedReservation,
    setSelectedReservation,
  };

  return (
    <ReservationContext.Provider value={contextValue}>
      {children}
    </ReservationContext.Provider>
  );
};

/**
 * 使用预约上下文的Hook
 */
export const useReservation = (): ReservationContextType => {
  const context = useContext(ReservationContext);
  if (context === undefined) {
    throw new Error('useReservation must be used within a ReservationProvider');
  }
  return context;
};

/**
 * 使用模板选择的Hook
 */
export const useTemplateSelection = () => {
  const { selectedTemplate, setSelectedTemplate } = useReservation();
  
  const selectTemplate = useCallback((template: EventTemplate) => {
    setSelectedTemplate(template);
  }, [setSelectedTemplate]);
  
  const clearTemplate = useCallback(() => {
    setSelectedTemplate(null);
  }, [setSelectedTemplate]);
  
  return {
    selectedTemplate,
    selectTemplate,
    clearTemplate,
  };
};

/**
 * 使用日历数据的Hook
 */
export const useCalendarData = () => {
  const { 
    calendarData, 
    calendarLoading, 
    calendarError, 
    loadCalendarData, 
    refreshCalendarData 
  } = useReservation();
  
  return {
    calendarData,
    loading: calendarLoading,
    error: calendarError,
    loadCalendarData,
    refreshCalendarData,
  };
};

/**
 * 使用预约表单的Hook
 */
export const useReservationForm = () => {
  const { 
    reservationForm, 
    updateReservationForm, 
    resetReservationForm,
    selectedTemplate,
    setSelectedTemplate
  } = useReservation();
  
  const setRoomName = useCallback((roomName: string) => {
    updateReservationForm({ roomName });
  }, [updateReservationForm]);
  
  const setSelectedDate = useCallback((selectedDate: Date | null) => {
    updateReservationForm({ selectedDate });
  }, [updateReservationForm]);
  
  const setScheduledDateTime = useCallback((scheduledDateTime: Date) => {
    updateReservationForm({ scheduledDateTime });
  }, [updateReservationForm]);
  
  const setDurationHours = useCallback((durationHours: number) => {
    updateReservationForm({ durationHours });
  }, [updateReservationForm]);
  
  return {
    // 表单数据
    roomName: reservationForm.roomName,
    selectedDate: reservationForm.selectedDate,
    scheduledDateTime: reservationForm.scheduledDateTime,
    durationHours: reservationForm.durationHours,
    selectedTemplate,
    
    // 更新方法
    setRoomName,
    setSelectedDate,
    setScheduledDateTime,
    setDurationHours,
    setSelectedTemplate,
    
    // 重置方法
    resetForm: resetReservationForm,
  };
};

/**
 * 使用预约详情的Hook
 */
export const useReservationDetail = () => {
  const { selectedReservation, setSelectedReservation } = useReservation();
  
  const selectReservation = useCallback((reservation: Reservation) => {
    setSelectedReservation(reservation);
  }, [setSelectedReservation]);
  
  const clearReservation = useCallback(() => {
    setSelectedReservation(null);
  }, [setSelectedReservation]);
  
  return {
    selectedReservation,
    selectReservation,
    clearReservation,
  };
};
