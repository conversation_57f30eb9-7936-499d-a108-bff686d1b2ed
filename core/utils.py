# your_app/utils.py

import logging
from typing import Optional, Dict, Any
from channels.db import database_sync_to_async

logger = logging.getLogger(__name__)

class StepInfo:
    """步骤信息类，用于统一处理用户模板步骤和系统模板步骤"""
    def __init__(self, order: int, step_type: str, name: str = "", duration: int = 300, configuration: Dict[Any, Any] = None):
        self.order = order
        self.step_type = step_type
        self.name = name
        self.duration = duration
        self.configuration = configuration or {}

def advance_to_next_step_sync(room) -> Optional[StepInfo]:
    """
    将房间推进到下一个环节，并返回下一个环节的对象。
    如果没有更多环节，则返回 None。

    现在使用房间专属的RoomEventStep模型，实现真正的实例解耦。
    """
    from .models import Room

    if room.status == Room.STATUS_CLOSED:
        return None

    # 使用房间的新方法来推进到下一个环节
    next_room_step = room.advance_to_next_event_step()

    if next_room_step:
        # 如果有下一个环节，返回StepInfo对象
        next_step = StepInfo(
            order=next_room_step.order,
            step_type=next_room_step.step_type,
            name=next_room_step.name,
            duration=next_room_step.duration,
            configuration=next_room_step.configuration
        )
        logger.info(f"房间 {room.room_code} 已推进到环节 {next_step.order}: {next_step.step_type}")
        return next_step
    else:
        # 如果没有更多环节，房间已经回到READY状态
        logger.info(f"房间 {room.room_code} 已完成当前环节，回到READY状态（大厅）等待房主添加新环节。")
        return None

# 异步版本，用于WebSocket消费者
advance_to_next_step = database_sync_to_async(advance_to_next_step_sync)

def _get_next_system_template_step(room) -> Optional[StepInfo]:
    """
    从系统模板获取下一个步骤

    Args:
        room: 房间对象

    Returns:
        StepInfo: 下一个步骤信息，如果没有则返回None
    """
    # 从房间的system_template字段获取系统模板
    if not room.system_template:
        logger.warning(f"房间 {room.room_code} 没有关联的系统模板，无法获取步骤")
        return None

    try:
        steps = room.system_template.get_steps()

        # 找到下一个步骤
        for step_data in steps:
            if step_data.get('order', 0) > room.current_step_order:
                return StepInfo(
                    order=step_data.get('order', 0),
                    step_type=step_data.get('step_type', 'FREE_CHAT'),
                    name=step_data.get('name', ''),
                    duration=step_data.get('duration', 300),
                    configuration=step_data.get('configuration', {})
                )

        return None

    except Exception as e:
        logger.error(f"获取系统模板步骤时发生错误: {e}")
        return None

# 删除重复的函数，现在只使用上面的版本

@database_sync_to_async
def save_room(room):
    """
    异步地保存房间对象到数据库。
    """
    try:
        room.save()
        logger.debug(f"成功保存房间 {room.room_code} 的状态。")
    except Exception as e:
        logger.error(f"保存房间 {room.room_code} 时出错: {e}")

# get_room_with_template 方法已被 RoomConsumer 内的 get_room_with_host 替代，
# 因此可以从这个文件中移除，以保持代码整洁。

