# core/urls.py

from django.urls import path
from .views import (
    HealthCheckView,
    RegisterView,
    RoomCreateView,
    RoomDetailView,
    APIRootView,
    JoinRoomView,
    CustomTokenObtainPairView,
    SubscriptionManagementView,
    ScheduleRoomView,
    CalendarDataView,
    RoomTemplateListView,
    ReservationDetailView,
    AddStepToRoomView,
    InsertStepToRoomView,
    DeleteStepFromRoomView,
    CheckInView,
    RoomStepsView,
)
from rest_framework_simplejwt.views import (
    TokenRefreshView,
)

urlpatterns = [
    # --- 添加这一行作为API的根路径 ---
    # 它会匹配到 /api/
    path('', APIRootView.as_view(), name='api-root'),

    # 认证和健康检查 - 统一路径协议
    path('health-check/', HealthCheckView.as_view(), name='health-check'),
    path('users/register/', RegisterView.as_view(), name='register'),
    path('users/login/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('users/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # 房间相关 - 统一路径协议
    path('rooms/', RoomCreateView.as_view(), name='room-create'),
    path('rooms/schedule/', ScheduleRoomView.as_view(), name='room-schedule'),
    path('rooms/join/', JoinRoomView.as_view(), name='room-join'),
    path('rooms/<str:room_code>/', RoomDetailView.as_view(), name='room-detail'),
    path('rooms/<str:room_code>/add-step/', AddStepToRoomView.as_view(), name='room-add-step'),
    path('rooms/<str:room_code>/events/insert/', InsertStepToRoomView.as_view(), name='room-insert-step'),
    path('rooms/<str:room_code>/events/<int:step_id>/', DeleteStepFromRoomView.as_view(), name='room-delete-step'),
    path('rooms/<str:room_code>/steps/', RoomStepsView.as_view(), name='room-steps'),

    # 订阅管理
    path('subscription/', SubscriptionManagementView.as_view(), name='subscription-management'),

    # 签到系统
    path('checkin/', CheckInView.as_view(), name='checkin'),

    # 日历和预约相关 - 统一路径协议
    path('calendar/', CalendarDataView.as_view(), name='calendar-data'),
    path('calendar/reservations/<int:reservation_id>/', ReservationDetailView.as_view(), name='reservation-detail'),
    path('templates/', RoomTemplateListView.as_view(), name='room-templates'),
]
