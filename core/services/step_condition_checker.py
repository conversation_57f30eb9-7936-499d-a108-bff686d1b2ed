"""
环节开始条件检查器

提供模块化的环节开始条件判断系统，支持不同类型环节的条件验证。
每个环节类型可以定义自己的开始条件，系统会自动检查并决定房间状态转换。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional
from django.db.models import Q

from ..models import Room, RoomParticipant, RoomState, UserState

logger = logging.getLogger(__name__)


class BaseStepConditionChecker(ABC):
    """环节开始条件检查器基类"""
    
    def __init__(self, step_type: str):
        self.step_type = step_type
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    @abstractmethod
    def check_conditions(self, room: Room) -> Tuple[bool, str, Dict[str, Any]]:
        """
        检查环节开始条件
        
        Args:
            room: 房间对象
            
        Returns:
            Tuple[bool, str, Dict[str, Any]]: (是否满足条件, 描述信息, 额外数据)
        """
        pass
    
    def get_active_participants(self, room: Room) -> List[RoomParticipant]:
        """获取房间中的活跃参与者"""
        return list(RoomParticipant.objects.filter(
            room=room,
            left_at__isnull=True
        ).select_related('user'))
    
    def get_ready_participants(self, room: Room) -> List[RoomParticipant]:
        """获取房间中准备就绪的参与者"""
        return list(RoomParticipant.objects.filter(
            room=room,
            left_at__isnull=True,
            state=UserState.READY
        ).select_related('user'))
    
    def has_host(self, room: Room) -> bool:
        """检查房间是否有房主"""
        return room.host is not None
    
    def get_host_participant(self, room: Room) -> Optional[RoomParticipant]:
        """获取房主参与者对象"""
        if not room.host:
            return None
        
        try:
            return RoomParticipant.objects.get(
                room=room,
                user=room.host,
                left_at__isnull=True,
                role=RoomParticipant.ROLE_HOST
            )
        except RoomParticipant.DoesNotExist:
            return None


class PictionaryStepConditionChecker(BaseStepConditionChecker):
    """你画我猜环节条件检查器"""
    
    def __init__(self):
        super().__init__('GAME_PICTIONARY')
    
    def check_conditions(self, room: Room) -> Tuple[bool, str, Dict[str, Any]]:
        """
        检查你画我猜环节开始条件
        
        条件：
        1. 至少有房主在房间中
        2. 至少有2个活跃参与者（包括房主）
        3. 房间状态允许开始游戏
        """
        # 检查房主
        if not self.has_host(room):
            return False, "需要房主在房间中", {}
        
        host_participant = self.get_host_participant(room)
        if not host_participant:
            return False, "房主未正确加入房间", {}
        
        # 获取活跃参与者
        active_participants = self.get_active_participants(room)
        participant_count = len(active_participants)
        
        # 检查最少人数要求
        min_participants = 2
        if participant_count < min_participants:
            return False, f"需要至少{min_participants}人参与，当前{participant_count}人", {
                'current_count': participant_count,
                'required_count': min_participants,
                'participants': [p.user.username for p in active_participants]
            }
        
        # 检查房间状态
        if room.status not in [RoomState.WAITING, RoomState.READY]:
            return False, f"房间状态不允许开始游戏: {room.get_status_display()}", {}
        
        # 所有条件满足
        return True, f"条件满足，可以开始你画我猜游戏（{participant_count}人参与）", {
            'participant_count': participant_count,
            'participants': [p.user.username for p in active_participants],
            'host': room.host.username
        }


class FreeChatStepConditionChecker(BaseStepConditionChecker):
    """自由讨论环节条件检查器"""
    
    def __init__(self):
        super().__init__('FREE_CHAT')
    
    def check_conditions(self, room: Room) -> Tuple[bool, str, Dict[str, Any]]:
        """
        检查自由讨论环节开始条件
        
        条件：
        1. 至少有房主在房间中
        2. 至少有1个活跃参与者（房主即可）
        3. 房间状态允许开始讨论
        """
        # 检查房主
        if not self.has_host(room):
            return False, "需要房主在房间中", {}
        
        host_participant = self.get_host_participant(room)
        if not host_participant:
            return False, "房主未正确加入房间", {}
        
        # 获取活跃参与者
        active_participants = self.get_active_participants(room)
        participant_count = len(active_participants)
        
        # 自由讨论只需要房主即可开始
        min_participants = 1
        if participant_count < min_participants:
            return False, f"需要至少{min_participants}人参与，当前{participant_count}人", {
                'current_count': participant_count,
                'required_count': min_participants,
                'participants': [p.user.username for p in active_participants]
            }
        
        # 检查房间状态
        if room.status not in [RoomState.WAITING, RoomState.READY]:
            return False, f"房间状态不允许开始讨论: {room.get_status_display()}", {}
        
        # 所有条件满足
        return True, f"条件满足，可以开始自由讨论（{participant_count}人参与）", {
            'participant_count': participant_count,
            'participants': [p.user.username for p in active_participants],
            'host': room.host.username
        }


class StepConditionCheckerFactory:
    """环节条件检查器工厂类"""
    
    _checkers = {
        'GAME_PICTIONARY': PictionaryStepConditionChecker,
        'FREE_CHAT': FreeChatStepConditionChecker,
    }
    
    @classmethod
    def create_checker(cls, step_type: str) -> Optional[BaseStepConditionChecker]:
        """
        创建指定类型的条件检查器
        
        Args:
            step_type: 环节类型
            
        Returns:
            BaseStepConditionChecker: 条件检查器实例，如果类型不支持则返回None
        """
        checker_class = cls._checkers.get(step_type)
        if checker_class:
            return checker_class()
        
        logger.warning(f"不支持的环节类型: {step_type}")
        return None
    
    @classmethod
    def register_checker(cls, step_type: str, checker_class: type):
        """
        注册新的条件检查器
        
        Args:
            step_type: 环节类型
            checker_class: 检查器类
        """
        cls._checkers[step_type] = checker_class
        logger.info(f"注册环节条件检查器: {step_type} -> {checker_class.__name__}")
    
    @classmethod
    def get_supported_types(cls) -> List[str]:
        """获取支持的环节类型列表"""
        return list(cls._checkers.keys())


class RoomConditionManager:
    """房间条件管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def check_next_step_conditions(self, room: Room) -> Tuple[bool, str, Dict[str, Any]]:
        """
        检查房间下一环节的开始条件
        
        Args:
            room: 房间对象
            
        Returns:
            Tuple[bool, str, Dict[str, Any]]: (是否满足条件, 描述信息, 额外数据)
        """
        # 获取下一个环节
        next_step = room.get_next_event_step()
        if not next_step:
            # 没有更多环节时，房间应该保持READY状态，允许房主添加新环节
            return True, "没有更多环节", {'reason': 'no_more_steps', 'all_steps_completed': True}
        
        # 创建对应的条件检查器
        checker = StepConditionCheckerFactory.create_checker(next_step.step_type)
        if not checker:
            # 如果没有专门的检查器，使用默认条件（只需要房主）
            return self._check_default_conditions(room)
        
        # 执行条件检查
        try:
            is_ready, message, extra_data = checker.check_conditions(room)
            extra_data['next_step'] = {
                'id': next_step.id,
                'name': next_step.name,
                'step_type': next_step.step_type,
                'order': next_step.order
            }
            
            self.logger.debug(f"房间 {room.room_code} 下一环节条件检查: {is_ready}, {message}")
            return is_ready, message, extra_data
            
        except Exception as e:
            self.logger.error(f"检查房间条件时发生错误: {e}", exc_info=True)
            return False, f"条件检查失败: {e}", {'error': str(e)}
    
    def _check_default_conditions(self, room: Room) -> Tuple[bool, str, Dict[str, Any]]:
        """检查默认条件（只需要房主）"""
        if not room.host:
            return False, "需要房主在房间中", {}
        
        try:
            host_participant = RoomParticipant.objects.get(
                room=room,
                user=room.host,
                left_at__isnull=True,
                role=RoomParticipant.ROLE_HOST
            )
            return True, "条件满足，可以开始下一环节", {
                'host': room.host.username
            }
        except RoomParticipant.DoesNotExist:
            return False, "房主未正确加入房间", {}


# 全局条件管理器实例
room_condition_manager = RoomConditionManager()
