# Generated by Django 5.2.4 on 2025-07-23 03:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0017_update_room_state_waiting'),
    ]

    operations = [
        migrations.CreateModel(
            name='RoomEventStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order', models.PositiveIntegerField(help_text='环节在当前房间流程中的顺序')),
                ('name', models.CharField(help_text='从模板环节复制的名称', max_length=100)),
                ('step_type', models.CharField(help_text='从模板环节复制的类型', max_length=50)),
                ('duration', models.PositiveIntegerField(default=300, help_text='从模板环节复制的时长（秒）')),
                ('configuration', models.JSONField(blank=True, default=dict, help_text='环节配置参数')),
                ('is_completed', models.BooleanField(default=False, help_text='环节是否已完成')),
                ('completed_at', models.DateTimeField(blank=True, help_text='环节完成时间', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='event_steps', to='core.room')),
            ],
            options={
                'verbose_name': '房间环节',
                'verbose_name_plural': '房间环节',
                'ordering': ['room', 'order'],
                'unique_together': {('room', 'order')},
            },
        ),
        migrations.AddField(
            model_name='room',
            name='current_event_step',
            field=models.ForeignKey(blank=True, help_text='当前正在执行的房间环节', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='current_in_rooms', to='core.roomeventstep'),
        ),
    ]
