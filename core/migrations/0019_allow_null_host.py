# Generated by Django 5.2.4 on 2025-07-23 09:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0018_add_room_event_step'),
    ]

    operations = [
        migrations.AlterField(
            model_name='room',
            name='host',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='hosted_rooms', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='systemtemplate',
            name='required_subscription',
            field=models.CharField(choices=[('Free', '免费版'), ('Pro', 'Pro版'), ('Max', 'Max版')], default='Free', help_text='使用此模板所需的最低订阅等级', max_length=10),
        ),
    ]
