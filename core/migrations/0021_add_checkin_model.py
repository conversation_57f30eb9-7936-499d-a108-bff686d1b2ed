# Generated by Django 5.2.4 on 2025-07-24 08:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0020_create_default_system_templates'),
    ]

    operations = [
        migrations.CreateModel(
            name='CheckIn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('check_in_date', models.DateField(help_text='签到日期')),
                ('check_in_time', models.DateTimeField(auto_now_add=True, help_text='签到时间')),
                ('check_in_type', models.CharField(choices=[('daily', '每日签到'), ('event', '活动签到')], default='daily', help_text='签到类型', max_length=20)),
                ('reward_points', models.IntegerField(default=1, help_text='签到获得的积分')),
                ('consecutive_days', models.IntegerField(default=1, help_text='连续签到天数')),
                ('user', models.ForeignKey(help_text='签到用户', on_delete=django.db.models.deletion.CASCADE, related_name='check_ins', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '签到记录',
                'verbose_name_plural': '签到记录',
                'ordering': ['-check_in_date', '-check_in_time'],
                'indexes': [models.Index(fields=['user', 'check_in_date'], name='core_checki_user_id_01a656_idx'), models.Index(fields=['check_in_date'], name='core_checki_check_i_d82c77_idx')],
                'unique_together': {('user', 'check_in_date', 'check_in_type')},
            },
        ),
    ]
