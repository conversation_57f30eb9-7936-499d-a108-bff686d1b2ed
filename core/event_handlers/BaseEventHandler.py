"""
团子APP事件处理器基础模块

本模块定义了所有游戏环节处理器的基础抽象类，提供统一的接口规范。
事件处理器是团子APP游戏逻辑的核心组件，负责处理不同类型活动环节的具体逻辑。

设计模式：
- 抽象基类模式：定义统一的处理器接口
- 策略模式：不同环节类型使用不同的处理策略
- 工厂模式：通过EventHandlerFactory创建具体处理器

核心职责：
1. 环节生命周期管理：启动、运行、结束、清理
2. 消息处理：处理用户在环节中的各种操作消息
3. 状态管理：维护环节的运行状态和数据
4. 超时处理：处理环节时间限制和自动推进
5. 错误处理：统一的错误处理和用户反馈

技术特性：
- 异步处理支持高并发
- 类型提示确保代码安全
- 循环依赖解决方案
- 统一的日志记录
- 可扩展的自定义动作支持

使用方式：
1. 继承BaseEventHandler创建具体处理器
2. 实现抽象方法定义环节逻辑
3. 通过EventHandlerFactory注册和创建
4. 在RoomConsumer中集成使用
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, TYPE_CHECKING

from ..models import Room
from events.models import EventStep

# 使用TYPE_CHECKING解决循环依赖问题
# 这些导入只在类型检查时处理，运行时不会导入
if TYPE_CHECKING:
    from ..consumers import RoomConsumer, get_room_with_host


class BaseEventHandler(ABC):
    """
    事件处理器基础抽象类

    这是所有游戏环节处理器的基础类，定义了环节处理的统一接口。
    每种游戏类型（如你画我猜、自由聊天等）都需要继承此类并实现具体逻辑。

    设计原则：
    1. 统一接口：所有处理器都遵循相同的方法签名
    2. 职责分离：每个处理器只负责一种环节类型
    3. 可扩展性：支持自定义动作和重启逻辑
    4. 错误处理：提供统一的错误处理机制

    生命周期：
    1. 创建：通过EventHandlerFactory创建实例
    2. 启动：调用start_step()初始化环节
    3. 运行：通过handle_message()处理用户操作
    4. 结束：调用handle_timeout()或自然结束
    5. 清理：调用cleanup()释放资源

    核心方法：
    - start_step(): 启动环节，返回初始状态
    - handle_message(): 处理用户消息和操作
    - handle_timeout(): 处理环节超时
    - handle_restart(): 处理重启请求
    - handle_custom_action(): 处理自定义动作
    """

    def __init__(self, room_code: str, consumer: 'RoomConsumer'):
        """
        初始化事件处理器

        Args:
            room_code (str): 房间代码，用于标识处理器所属房间
            consumer (RoomConsumer): WebSocket消费者实例，用于消息通信

        Note:
            使用前向引用解决循环依赖问题
        """
        self.room_code = room_code
        self.consumer = consumer
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

    @abstractmethod
    async def start_step(self, room: Room, step: EventStep) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        启动环节的抽象方法。

        Args:
            room: 房间对象
            step: 环节步骤对象

        Returns:
            Tuple[payload_data, error_message]: 成功时返回(数据, None)，失败时返回(None, 错误信息)
        """
        pass

    @abstractmethod
    async def handle_message(self, user, payload: Dict[str, Any]) -> bool:
        """
        处理聊天消息的抽象方法。

        Args:
            user: 发送消息的用户
            payload: 消息载荷

        Returns:
            bool: True表示消息已被处理，False表示应继续常规处理
        """
        pass

    @abstractmethod
    async def handle_timeout(self) -> None:
        """
        处理环节超时的抽象方法。
        """
        pass

    async def handle_restart(self, user, payload: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        处理重启请求的默认实现。子类可以重写此方法。
        """
        _ = user, payload
        return None, "此环节不支持重启功能"

    async def handle_custom_action(self, action: str, user, payload: Dict[str, Any]) -> bool:
        """
        处理自定义动作的默认实现。子类可以重写此方法。
        """
        _ = action, user, payload
        return False

    async def cleanup(self) -> None:
        """
        清理资源的默认实现。子类可以重写此方法。
        """
        pass

    # --- Helper Methods ---

    # This method now correctly uses the function imported from utils.py
    async def get_room_with_template(self) -> Optional[Room]:
        """获取带有模板的房间对象"""
        return await get_room_with_host(self.room_code)

    async def broadcast_to_room(self, message_type: str, payload: Dict[str, Any]) -> None:
        """向房间广播消息"""
        # This works because self.consumer is a real RoomConsumer instance at runtime
        await self.consumer.channel_layer.group_send(
            self.consumer.room_group_name,
            {'type': message_type, 'payload': payload}
        )

    async def send_error_to_user(self, message: str) -> None:
        """向用户发送错误消息"""
        await self.consumer.send_error(message)
