"""
团子APP核心视图模块

本模块提供团子APP的核心API视图，包括：
- 用户认证和注册功能
- 房间管理和操作API
- 房间参与者管理
- 健康检查和状态监控
- 订阅系统集成

主要功能模块：
1. 用户管理：注册、登录、JWT令牌管理
2. 房间生命周期：创建、加入、离开、状态管理
3. 房间操作：环节管理、参与者权限控制
4. 预约系统：预约房间创建和管理
5. 模板系统：系统模板和用户模板的统一访问

技术特性：
- 基于Django REST Framework的API设计
- JWT令牌认证和权限控制
- 完整的错误处理和日志记录
- 订阅等级的权限验证
- 实时房间状态管理
"""

import uuid
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from django.db import models
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from rest_framework.permissions import IsAuthenticated, AllowAny

# 确保引入了所有需要的模型和序列化器
from .models import User, Room, RoomParticipant, RoomState
from .serializers import UserSerializer, RoomSerializer, CustomTokenObtainPairSerializer
from .services.room_manager import room_manager
from .exceptions import handle_room_exception, RoomSystemException
from events.models import EventTemplate, EventStep # 确保引入 EventStep
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken

logger = logging.getLogger(__name__)


class APIRootView(APIView):
    def get(self, request, *args, **kwargs):
        return Response({"message": "Welcome to the Tuanzi API!"})

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [AllowAny]

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer

class HealthCheckView(APIView):
    """
    健康检查端点
    用于验证用户认证状态和会话有效性
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """返回用户认证状态"""
        logger.debug(f"健康检查请求，用户: {request.user.username}")

        return Response({
            'status': 'ok',
            'authenticated': True,
            'user': request.user.username,
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_200_OK)


class RoomCreateView(APIView):
    """
    房间创建API视图

    此视图负责处理即时房间的创建请求，包括：
    1. 用户认证和权限验证
    2. 模板选择和验证
    3. 订阅等级限制检查
    4. 房间代码生成和配置
    5. 房间环节的初始化

    支持的功能：
    - 基于用户模板或系统模板创建房间
    - 自动设置订阅等级对应的房间限制
    - 生成唯一的房间代码
    - 创建房间专属的环节副本
    - 自动将创建者设为房主

    权限要求：
    - 用户必须已认证
    - 用户必须有足够的订阅权限使用选定模板
    - 用户不能超过每日房间创建限制
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """
        创建新房间的核心处理方法

        处理流程：
        1. 验证用户认证状态
        2. 检查模板ID的有效性
        3. 验证用户对模板的访问权限
        4. 检查每日房间创建限制
        5. 使用房间管理器创建房间
        6. 返回房间信息给客户端

        请求参数：
        - template_id (str): 模板ID，格式为'system_1'或'user_1'

        返回数据：
        - 成功：房间详细信息（房间代码、状态、参与者等）
        - 失败：错误信息和对应的HTTP状态码
        """
        logger.info(f"收到创建房间请求，用户: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
        logger.debug(f"请求数据: {request.data}")

        # 验证用户认证
        if not request.user.is_authenticated:
            logger.warning("未认证用户尝试创建房间")
            return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

        template_id = request.data.get('template_id')
        if template_id is None:
            logger.warning(f"用户 {request.user.username} 创建房间时未提供模板ID")
            return Response({"error": "Template ID is required."}, status=status.HTTP_400_BAD_REQUEST)

        # 验证模板存在性和权限 - 使用新的模板管理器
        try:
            from .models import TemplateManager
            template_obj, template_type = TemplateManager.get_template_by_id(template_id, request.user)

            if not template_obj:
                logger.warning(f"用户 {request.user.username} 尝试使用不存在或无权限的模板ID: {template_id}")
                return Response({"error": "Template not found or access denied."}, status=status.HTTP_404_NOT_FOUND)

            logger.info(f"找到模板: {template_obj.name} (ID: {template_id}, Type: {template_type})")
        except Exception as e:
            logger.error(f"验证模板时发生错误: {e}")
            return Response({"error": "Template validation failed."}, status=status.HTTP_400_BAD_REQUEST)

        host = request.user

        # 每日房间创建限制检查
        can_create, error_msg, remaining = host.check_daily_room_creation_limit()
        if not can_create:
            logger.info(f"用户 {host.username} 达到每日房间创建限制")
            return Response({
                "error": error_msg,
                "daily_limit_reached": True,
                "subscription_level": host.subscription_level
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)

        logger.info(f"用户 {host.username} 今日还可创建 {remaining} 个房间")

        # 订阅等级检查
        if host.subscription_level == host.SUBSCRIPTION_FREE:
            if template_type == 'user':
                # 用户模板：检查EventStep
                premium_steps = template_obj.steps.filter(step_type__in=EventStep.PREMIUM_STEP_TYPES)
                if premium_steps.exists():
                    premium_step_names = [step.get_step_type_display() for step in premium_steps]
                    logger.info(f"免费用户 {host.username} 尝试使用付费模板，包含付费环节: {premium_step_names}")
                    return Response({
                        "error": "此模板包含付费专属环节，请升级到Pro版本以使用。",
                        "premium_steps": premium_step_names,
                        "upgrade_required": True
                    }, status=status.HTTP_403_FORBIDDEN)
            elif template_type == 'system':
                # 系统模板：检查模板本身的订阅要求
                if not template_obj.is_accessible_by_user(host):
                    logger.info(f"免费用户 {host.username} 尝试使用需要更高订阅等级的系统模板: {template_obj.name}")
                    return Response({
                        "error": f"此模板需要 {template_obj.get_required_subscription_display()} 订阅等级。",
                        "required_subscription": template_obj.required_subscription,
                        "upgrade_required": True
                    }, status=status.HTTP_403_FORBIDDEN)

        try:
            logger.info(f"开始为用户 {host.username} 创建房间，使用模板: {template_obj.name}")

            # 创建房间 - 直接使用数据库操作而不是房间管理器
            new_room = self._create_room_with_template(
                host=host,
                template_obj=template_obj,
                template_type=template_type
            )
            logger.info(f"房间创建成功: {new_room.room_code}, 房主: {host.username}")

            # 序列化并返回房间数据
            serializer = RoomSerializer(new_room)
            logger.info(f"返回房间数据给用户 {host.username}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except RoomSystemException as e:
            logger.error(f"房间系统异常: {e.message}, 用户: {host.username}")
            return Response(handle_room_exception(e), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"创建房间时发生未预期错误: {e}, 用户: {host.username}", exc_info=True)
            return Response({
                "error": "创建房间时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _create_room_with_template(self, host, template_obj, template_type):
        """
        创建房间，支持系统模板和用户模板
        """
        from django.db import transaction
        import uuid
        from .models import RoomParticipant, RoomState, TemplateManager

        with transaction.atomic():
            # 生成唯一房间号
            while True:
                room_code = uuid.uuid4().hex.upper()[:6]
                if not Room.objects.filter(room_code=room_code).exists():
                    break

            # 根据模板类型设置相应的字段
            if template_type == 'user':
                event_template = template_obj
                system_template = None
            else:  # template_type == 'system'
                event_template = None
                system_template = template_obj

            room = Room.objects.create(
                room_code=room_code,
                host=host,
                event_template=event_template,
                system_template=system_template,
                status=RoomState.OPEN
            )

            # 设置订阅限制
            room.set_limits_by_subscription(host)
            room.save()

            # 将房主添加为参与者
            RoomParticipant.objects.create(
                room=room,
                user=host,
                role=RoomParticipant.ROLE_HOST,
                state='JOINED'
            )

            # 房主加入后，触发状态转换检查
            # OPEN -> WAITING（有人加入房间）-> READY（满足条件）
            from .services.room_manager import room_manager
            room.status = RoomState.WAITING
            room.save()

            # 检查房间条件并可能转换到READY状态
            room_manager._check_room_conditions_and_transition_sync(room)

            # 复制模板步骤到房间
            TemplateManager.copy_template_steps_to_room(template_obj, template_type, room)

            return room


class JoinRoomView(APIView):
    """
    房间加入API视图

    此视图处理用户加入现有房间的请求，包括：
    1. 房间代码验证和房间查找
    2. 房间状态和容量检查
    3. 用户权限和重复加入检查
    4. 参与者记录创建和更新
    5. 房间状态的动态调整

    加入条件检查：
    - 房间必须存在且未关闭
    - 房间未达到最大参与人数限制
    - 房间未过期
    - 用户未被禁止加入

    状态转换逻辑：
    - OPEN -> WAITING：首次有用户加入
    - WAITING -> READY：满足活动开始条件

    权限要求：
    - 用户必须已认证
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """
        用户加入房间的核心处理方法

        处理流程：
        1. 验证用户认证状态
        2. 检查房间代码的有效性
        3. 查找目标房间并验证状态
        4. 检查房间加入条件
        5. 使用房间管理器处理加入逻辑
        6. 返回更新后的房间信息

        请求参数：
        - room_code (str): 6位房间代码

        返回数据：
        - 成功：更新后的房间详细信息
        - 失败：错误信息和对应的HTTP状态码
        """
        logger.info(f"收到加入房间请求，用户: {request.user.username if request.user.is_authenticated else 'Anonymous'}")
        logger.debug(f"请求数据: {request.data}")

        # 验证用户认证
        if not request.user.is_authenticated:
            logger.warning("未认证用户尝试加入房间")
            return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

        room_code = request.data.get('room_code')
        if not room_code:
            logger.warning(f"用户 {request.user.username} 加入房间时未提供房间代码")
            return Response(
                {"error": "Room code is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        user = request.user
        logger.info(f"用户 {user.username} 尝试加入房间: {room_code}")

        try:
            # 使用同步方法调用房间管理器
            success, message = room_manager.join_room_sync(room_code, user)

            if success:
                logger.info(f"用户 {user.username} 成功加入房间 {room_code}")
                # 获取房间信息并返回
                room = room_manager.get_room_sync(room_code)
                serializer = RoomSerializer(room)
                return Response({
                    "message": message,
                    "room": serializer.data
                }, status=status.HTTP_200_OK)
            else:
                logger.warning(f"用户 {user.username} 加入房间 {room_code} 失败: {message}")
                # 根据错误消息确定HTTP状态码
                if "不存在" in message:
                    status_code = status.HTTP_404_NOT_FOUND
                elif "已满" in message or "过期" in message or "不允许" in message or "已关闭" in message:
                    status_code = status.HTTP_403_FORBIDDEN
                else:
                    status_code = status.HTTP_400_BAD_REQUEST

                return Response({"error": message}, status=status_code)

        except RoomSystemException as e:
            logger.error(f"房间系统异常: {e.message}, 用户: {user.username}")
            return Response(handle_room_exception(e), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"加入房间时发生未预期错误: {e}, 用户: {user.username}", exc_info=True)
            return Response({
                "error": "加入房间时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RoomDetailView(APIView):
    """
    获取房间详情
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, room_code, *args, **kwargs):
        """获取房间详情"""
        logger.info(f"收到获取房间详情请求，房间: {room_code}, 用户: {request.user.username}")

        try:
            # 获取房间信息
            room = room_manager.get_room_sync(room_code)

            # 序列化并返回房间数据
            serializer = RoomSerializer(room)
            logger.info(f"返回房间 {room_code} 详情给用户 {request.user.username}")
            return Response(serializer.data, status=status.HTTP_200_OK)

        except RoomSystemException as e:
            logger.error(f"获取房间详情时发生房间系统异常: {e.message}, 房间: {room_code}")
            return Response(handle_room_exception(e), status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"获取房间详情时发生未预期错误: {e}, 房间: {room_code}", exc_info=True)
            return Response({
                "error": "获取房间详情时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SubscriptionManagementView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        user = request.user
        return Response({
            'current_level': user.subscription_level,
            'username': user.username,
            'subscription_info': {
                'Free': {'max_participants': 10, 'duration_hours': 2},
                'Pro': {'max_participants': 500, 'duration_hours': 24},
                'Max': {'max_participants': 2000, 'duration_hours': 72},
            }
        })

    def post(self, request, *args, **kwargs):
        target_level = request.data.get('target_level')
        is_debug = request.data.get('is_debug', False)
        if target_level not in [User.SUBSCRIPTION_FREE, User.SUBSCRIPTION_PRO, User.SUBSCRIPTION_MAX]:
            return Response(
                {"error": "Invalid subscription level"},
                status=status.HTTP_400_BAD_REQUEST
            )
        user = request.user
        if is_debug and hasattr(request, 'META') and request.META.get('HTTP_X_DEBUG_MODE'):
            user.subscription_level = target_level
            user.save()
            refresh = RefreshToken.for_user(user)
            return Response({
                'message': f'Debug: Subscription level changed to {target_level}',
                'new_level': target_level,
                'access_token': str(refresh.access_token),
                'refresh_token': str(refresh)
            })
        return Response({
            'message': 'Payment integration not implemented yet',
        }, status=status.HTTP_501_NOT_IMPLEMENTED)


class ScheduleRoomView(APIView):
    """
    预约房间API端点
    允许用户预约未来某个时间的游戏房间
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        """创建预约房间"""
        logger.info(f"收到预约房间请求，用户: {request.user.username}")
        logger.debug(f"请求数据: {request.data}")

        # 验证用户认证
        if not request.user.is_authenticated:
            logger.warning("未认证用户尝试预约房间")
            return Response({"error": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)

        # 获取请求参数
        name = request.data.get('name')
        template_id = request.data.get('template_id')
        scheduled_start_time_str = request.data.get('scheduled_start_time')
        duration_hours = request.data.get('duration_hours')

        # 参数验证
        if not all([name, template_id, scheduled_start_time_str, duration_hours]):
            missing_fields = []
            if not name: missing_fields.append('name')
            if not template_id: missing_fields.append('template_id')
            if not scheduled_start_time_str: missing_fields.append('scheduled_start_time')
            if not duration_hours: missing_fields.append('duration_hours')

            logger.warning(f"用户 {request.user.username} 预约房间时缺少必要参数: {missing_fields}")
            return Response({
                "error": "Missing required fields",
                "missing_fields": missing_fields
            }, status=status.HTTP_400_BAD_REQUEST)

        # 解析预约时间
        try:
            # 尝试解析ISO格式的时间字符串
            scheduled_start_time = parse_datetime(scheduled_start_time_str)
            if not scheduled_start_time:
                # 如果parse_datetime失败，尝试其他格式
                scheduled_start_time = datetime.fromisoformat(scheduled_start_time_str.replace('Z', '+00:00'))

            # 确保时间是timezone-aware的
            if scheduled_start_time.tzinfo is None:
                scheduled_start_time = timezone.make_aware(scheduled_start_time)

        except (ValueError, TypeError) as e:
            logger.warning(f"用户 {request.user.username} 提供的预约时间格式无效: {scheduled_start_time_str}")
            return Response({
                "error": "Invalid scheduled_start_time format. Use ISO format (e.g., '2023-12-25T14:30:00Z')"
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证预约时间不能是过去时间
        if scheduled_start_time <= timezone.now():
            logger.warning(f"用户 {request.user.username} 尝试预约过去的时间: {scheduled_start_time}")
            return Response({
                "error": "Scheduled time must be in the future"
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证持续时长
        try:
            duration_hours = int(duration_hours)
            if duration_hours <= 0 or duration_hours > 72:  # 最大72小时
                raise ValueError("Duration must be between 1 and 72 hours")
        except (ValueError, TypeError):
            logger.warning(f"用户 {request.user.username} 提供的持续时长无效: {duration_hours}")
            return Response({
                "error": "Invalid duration_hours. Must be an integer between 1 and 72"
            }, status=status.HTTP_400_BAD_REQUEST)

        # 模板ID现在支持字符串格式（如 'system_1' 或 'user_1'），不需要转换为整数
        # 直接使用原始的template_id进行后续处理

        # 验证模板存在性和权限
        try:
            from .models import TemplateManager
            template_obj, template_type = TemplateManager.get_template_by_id(template_id, request.user)

            if not template_obj:
                logger.warning(f"用户 {request.user.username} 尝试使用不存在或无权限的模板ID: {template_id}")
                return Response({"error": "Template not found or access denied."}, status=status.HTTP_404_NOT_FOUND)

            logger.info(f"找到模板: {template_obj.name} (ID: {template_id}, Type: {template_type})")
        except Exception as e:
            logger.error(f"验证模板时发生错误: {e}")
            return Response({"error": "Template validation failed."}, status=status.HTTP_400_BAD_REQUEST)

        host = request.user

        # 每日房间创建限制检查
        can_create, error_msg, remaining = host.check_daily_room_creation_limit()
        if not can_create:
            logger.info(f"用户 {host.username} 达到每日房间创建限制")
            return Response({
                "error": error_msg,
                "daily_limit_reached": True,
                "subscription_level": host.subscription_level
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)

        logger.info(f"用户 {host.username} 今日还可创建 {remaining} 个房间")

        # 订阅等级检查
        if host.subscription_level == host.SUBSCRIPTION_FREE:
            if template_type == 'user':
                # 用户模板：检查EventStep
                premium_steps = template_obj.steps.filter(step_type__in=EventStep.PREMIUM_STEP_TYPES)
                if premium_steps.exists():
                    premium_step_names = [step.get_step_type_display() for step in premium_steps]
                    logger.info(f"免费用户 {host.username} 尝试预约使用付费模板，包含付费环节: {premium_step_names}")
                    return Response({
                        "error": "此模板包含付费专属环节，请升级到Pro版本以使用。",
                        "premium_steps": premium_step_names,
                        "upgrade_required": True
                    }, status=status.HTTP_403_FORBIDDEN)
            elif template_type == 'system':
                # 系统模板：检查模板本身的订阅要求
                if not template_obj.is_accessible_by_user(host):
                    logger.info(f"免费用户 {host.username} 尝试使用需要更高订阅等级的系统模板: {template_obj.name}")
                    return Response({
                        "error": f"此模板需要 {template_obj.get_required_subscription_display()} 订阅等级。",
                        "required_subscription": template_obj.required_subscription,
                        "upgrade_required": True
                    }, status=status.HTTP_403_FORBIDDEN)

        try:
            logger.info(f"开始为用户 {host.username} 创建预约房间，使用模板: {template_obj.name}")

            # 创建预约房间
            room = self._create_scheduled_room(
                host=host,
                name=name,
                template_obj=template_obj,
                template_type=template_type,
                scheduled_start_time=scheduled_start_time,
                duration_hours=duration_hours
            )

            logger.info(f"预约房间创建成功: {room.room_code}, 房主: {host.username}, 预约时间: {scheduled_start_time}")

            # 序列化并返回房间数据
            serializer = RoomSerializer(room)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"创建预约房间时发生错误: {e}, 用户: {host.username}", exc_info=True)
            return Response({
                "error": "创建预约房间时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _create_scheduled_room(self, host, name, template_obj, template_type, scheduled_start_time, duration_hours):
        """
        创建预约房间的内部方法
        """
        from django.db import transaction
        from .models import TemplateManager

        with transaction.atomic():
            # 生成唯一房间号
            while True:
                room_code = uuid.uuid4().hex.upper()[:6]
                if not Room.objects.filter(room_code=room_code).exists():
                    break

            # 创建预约房间
            # 根据模板类型设置相应的字段
            if template_type == 'user':
                event_template = template_obj
                system_template = None
            else:  # template_type == 'system'
                event_template = None
                system_template = template_obj

            room = Room.objects.create(
                room_code=room_code,
                name=name,
                host=host,
                event_template=event_template,
                system_template=system_template,
                status=RoomState.SCHEDULED,
                scheduled_start_time=scheduled_start_time,
                duration_hours=duration_hours
            )

            # 设置订阅限制
            room.set_limits_by_subscription(host)

            # 设置预约房间的过期时间
            room.set_scheduled_expiry()
            room.save()

            # 将房主添加为参与者
            RoomParticipant.objects.create(
                room=room,
                user=host,
                role=RoomParticipant.ROLE_HOST,
                state='JOINED'
            )

            # 复制模板步骤到房间
            TemplateManager.copy_template_steps_to_room(template_obj, template_type, room)

            return room


class ReservationDetailView(APIView):
    """
    预约详情API端点
    提供单个预约的详细信息，包括模板详情和参与者信息
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, reservation_id, *args, **kwargs):
        """获取预约详情"""
        logger.info(f"收到获取预约详情请求，预约ID: {reservation_id}, 用户: {request.user.username}")

        try:
            # 获取预约房间
            room = Room.objects.select_related(
                'host', 'event_template', 'system_template'
            ).prefetch_related(
                'room_participants__user'
            ).get(
                id=reservation_id,
                status=RoomState.SCHEDULED
            )

            # 检查权限：只有房主或参与者可以查看详情
            user_participant = room.room_participants.filter(user=request.user).first()
            if room.host != request.user and not user_participant:
                logger.warning(f"用户 {request.user.username} 尝试访问无权限的预约 {reservation_id}")
                return Response({
                    "error": "您没有权限查看此预约详情"
                }, status=status.HTTP_403_FORBIDDEN)

            # 计算结束时间
            end_time = room.scheduled_start_time + timedelta(hours=room.duration_hours)

            # 获取模板详情
            template_details = None
            if room.event_template:
                template_details = {
                    'name': room.event_template.name,
                    'description': room.event_template.description,
                    'steps': [
                        {
                            'id': step.id,
                            'name': step.name,
                            'step_type': step.step_type,
                            'duration': step.duration,
                            'order': step.order
                        }
                        for step in room.event_template.steps.all().order_by('order')
                    ]
                }
            elif room.system_template:
                template_details = {
                    'name': room.system_template.name,
                    'description': room.system_template.description,
                    'steps': room.system_template.get_steps()
                }

            # 获取参与者信息
            participants = [
                {
                    'username': participant.user.username,
                    'role': participant.role,
                    'joined_at': participant.joined_at.isoformat()
                }
                for participant in room.room_participants.filter(left_at__isnull=True)
            ]

            # 构建响应数据
            reservation_data = {
                'id': room.id,
                'room_code': room.room_code,
                'name': room.name,
                'host_username': room.host.username,
                'start_time': room.scheduled_start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_hours': room.duration_hours,
                'max_participants': room.max_participants,
                'current_participants': room.get_participant_count(),
                'template_id': room.event_template.id if room.event_template else (room.system_template.id if room.system_template else None),
                'template_name': room.event_template.name if room.event_template else (room.system_template.name if room.system_template else None),
                'template_details': template_details,
                'participants': participants,
                'created_at': room.created_at.isoformat(),
            }

            logger.info(f"返回预约 {reservation_id} 详情给用户 {request.user.username}")
            return Response(reservation_data, status=status.HTTP_200_OK)

        except Room.DoesNotExist:
            logger.warning(f"用户 {request.user.username} 尝试访问不存在的预约 {reservation_id}")
            return Response({
                "error": "预约不存在或已被取消"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"获取预约详情时发生错误: {e}, 预约ID: {reservation_id}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "获取预约详情时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, reservation_id, *args, **kwargs):
        """修改预约"""
        logger.info(f"收到修改预约请求，预约ID: {reservation_id}, 用户: {request.user.username}")
        logger.debug(f"请求数据: {request.data}")

        try:
            # 获取预约房间
            room = Room.objects.get(
                id=reservation_id,
                status=RoomState.SCHEDULED
            )

            # 检查权限：只有房主可以修改预约
            if room.host != request.user:
                logger.warning(f"用户 {request.user.username} 尝试修改非自己的预约 {reservation_id}")
                return Response({
                    "error": "只有房主可以修改预约"
                }, status=status.HTTP_403_FORBIDDEN)

            # 检查是否可以修改（例如：开始前1小时内不能修改）
            time_until_start = room.scheduled_start_time - timezone.now()
            if time_until_start.total_seconds() < 60 * 60:  # 1小时
                logger.warning(f"用户 {request.user.username} 尝试在开始前1小时内修改预约 {reservation_id}")
                return Response({
                    "error": "预约开始前1小时内不能修改"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取修改参数
            name = request.data.get('name')
            scheduled_start_time_str = request.data.get('scheduled_start_time')
            duration_hours = request.data.get('duration_hours')

            # 验证参数
            if name is not None:
                if not name.strip():
                    return Response({
                        "error": "房间名称不能为空"
                    }, status=status.HTTP_400_BAD_REQUEST)
                room.name = name.strip()

            if scheduled_start_time_str is not None:
                try:
                    scheduled_start_time = parse_datetime(scheduled_start_time_str)
                    if not scheduled_start_time:
                        raise ValueError("Invalid datetime format")
                    if scheduled_start_time.tzinfo is None:
                        scheduled_start_time = timezone.make_aware(scheduled_start_time)

                    # 验证新的预约时间不能是过去时间
                    if scheduled_start_time <= timezone.now():
                        return Response({
                            "error": "预约时间必须是未来时间"
                        }, status=status.HTTP_400_BAD_REQUEST)

                    room.scheduled_start_time = scheduled_start_time
                    # 重新设置过期时间
                    room.set_scheduled_expiry()

                except (ValueError, TypeError):
                    return Response({
                        "error": "无效的时间格式"
                    }, status=status.HTTP_400_BAD_REQUEST)

            if duration_hours is not None:
                try:
                    duration_hours = int(duration_hours)
                    if duration_hours <= 0 or duration_hours > 72:
                        raise ValueError("Duration must be between 1 and 72 hours")
                    room.duration_hours = duration_hours
                except (ValueError, TypeError):
                    return Response({
                        "error": "持续时长必须在1-72小时之间"
                    }, status=status.HTTP_400_BAD_REQUEST)

            # 保存修改
            room.save()

            logger.info(f"预约 {reservation_id} 已被用户 {request.user.username} 修改")

            # 返回更新后的预约信息
            serializer = RoomSerializer(room)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Room.DoesNotExist:
            logger.warning(f"用户 {request.user.username} 尝试修改不存在的预约 {reservation_id}")
            return Response({
                "error": "预约不存在或已被取消"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"修改预约时发生错误: {e}, 预约ID: {reservation_id}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "修改预约时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, reservation_id, *args, **kwargs):
        """取消预约"""
        logger.info(f"收到取消预约请求，预约ID: {reservation_id}, 用户: {request.user.username}")

        try:
            # 获取预约房间
            room = Room.objects.get(
                id=reservation_id,
                status=RoomState.SCHEDULED
            )

            # 检查权限：只有房主可以取消预约
            if room.host != request.user:
                logger.warning(f"用户 {request.user.username} 尝试取消非自己的预约 {reservation_id}")
                return Response({
                    "error": "只有房主可以取消预约"
                }, status=status.HTTP_403_FORBIDDEN)

            # 检查是否可以取消（例如：开始前30分钟内不能取消）
            time_until_start = room.scheduled_start_time - timezone.now()
            if time_until_start.total_seconds() < 30 * 60:  # 30分钟
                logger.warning(f"用户 {request.user.username} 尝试在开始前30分钟内取消预约 {reservation_id}")
                return Response({
                    "error": "预约开始前30分钟内不能取消"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 取消预约（删除房间）
            room_code = room.room_code
            room.delete()

            logger.info(f"预约 {reservation_id} (房间 {room_code}) 已被用户 {request.user.username} 取消")
            return Response({
                "message": "预约已成功取消"
            }, status=status.HTTP_200_OK)

        except Room.DoesNotExist:
            logger.warning(f"用户 {request.user.username} 尝试取消不存在的预约 {reservation_id}")
            return Response({
                "error": "预约不存在或已被取消"
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"取消预约时发生错误: {e}, 预约ID: {reservation_id}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "取消预约时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CalendarDataView(APIView):
    """
    日历数据API端点
    提供日历界面所需的预约房间数据
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """获取日历预约数据"""
        logger.info(f"收到日历数据请求，用户: {request.user.username}")

        # 获取查询参数
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        month_str = request.GET.get('month')  # 格式: YYYY-MM

        try:
            # 确定查询的时间范围
            if month_str:
                # 按月查询
                year, month = map(int, month_str.split('-'))
                start_date = timezone.datetime(year, month, 1)
                if month == 12:
                    end_date = timezone.datetime(year + 1, 1, 1)
                else:
                    end_date = timezone.datetime(year, month + 1, 1)
                start_date = timezone.make_aware(start_date)
                end_date = timezone.make_aware(end_date)
            elif start_date_str and end_date_str:
                # 按日期范围查询
                start_date = parse_datetime(start_date_str)
                end_date = parse_datetime(end_date_str)
                if not start_date or not end_date:
                    raise ValueError("Invalid date format")
                if start_date.tzinfo is None:
                    start_date = timezone.make_aware(start_date)
                if end_date.tzinfo is None:
                    end_date = timezone.make_aware(end_date)
            else:
                # 默认查询当前月份
                now = timezone.now()
                start_date = timezone.datetime(now.year, now.month, 1)
                if now.month == 12:
                    end_date = timezone.datetime(now.year + 1, 1, 1)
                else:
                    end_date = timezone.datetime(now.year, now.month + 1, 1)
                start_date = timezone.make_aware(start_date)
                end_date = timezone.make_aware(end_date)

        except (ValueError, TypeError) as e:
            logger.warning(f"用户 {request.user.username} 提供的日期参数无效: {e}")
            return Response({
                "error": "Invalid date parameters. Use 'month' (YYYY-MM) or 'start_date' and 'end_date' (ISO format)"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 查询指定时间范围内的预约房间（只显示用户自己的预约）
            scheduled_rooms = Room.objects.filter(
                status=RoomState.SCHEDULED,
                host=request.user,  # 只显示用户自己创建的预约
                scheduled_start_time__gte=start_date,
                scheduled_start_time__lt=end_date
            ).select_related('host', 'event_template', 'system_template').order_by('scheduled_start_time')

            # 构建响应数据
            reservations = []
            for room in scheduled_rooms:
                # 计算结束时间
                end_time = room.scheduled_start_time + timedelta(hours=room.duration_hours)

                # 优先使用房间名称，如果没有则使用模板名称
                room_name = room.name
                if not room_name:
                    if room.event_template:
                        room_name = room.event_template.name
                    elif room.system_template:
                        room_name = room.system_template.name
                    else:
                        room_name = 'Unknown Event'

                reservation_data = {
                    'id': room.id,
                    'room_code': room.room_code,
                    'name': room_name,
                    'host_username': room.host.username,
                    'start_time': room.scheduled_start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'duration_hours': room.duration_hours,
                    'max_participants': room.max_participants,
                    'current_participants': room.get_participant_count(),
                    'template_id': room.event_template.id if room.event_template else (room.system_template.id if room.system_template else None),
                    'template_name': room.event_template.name if room.event_template else (room.system_template.name if room.system_template else None),
                    'created_at': room.created_at.isoformat(),
                }
                reservations.append(reservation_data)

            logger.info(f"返回 {len(reservations)} 个预约记录给用户 {request.user.username}")

            return Response({
                'reservations': reservations,
                'query_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'total_count': len(reservations)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取日历数据时发生错误: {e}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "获取日历数据时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RoomTemplateListView(APIView):
    """
    房间模板列表API
    提供高性能的模板查询，支持缓存
    统一返回系统模板和用户模板
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """获取可用的房间模板列表"""
        logger.info(f"收到房间模板列表请求，用户: {request.user.username}")

        try:
            from .models import TemplateManager

            # 使用模板管理器获取用户可用的所有模板
            template_list = TemplateManager.get_available_templates_for_user(request.user)

            logger.info(f"返回 {len(template_list)} 个模板给用户 {request.user.username}")
            logger.debug(f"模板列表: {[t['name'] for t in template_list]}")

            return Response({
                'templates': template_list,
                'total_count': len(template_list)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取房间模板时发生错误: {e}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "获取房间模板时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AddStepToRoomView(APIView):
    """
    动态添加环节到房间的API端点

    这是持久化房间系统的核心功能：
    - 允许房主在环节结束后动态添加新环节
    - 支持从预定义的环节类型中选择
    - 验证房主权限和房间状态
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, room_code, *args, **kwargs):
        """
        为指定房间添加新环节

        请求体参数：
        - step_type: 环节类型 (GAME_PICTIONARY, FREE_CHAT, 等)
        - name: 环节名称 (可选)
        - duration: 环节持续时间，秒 (可选，默认300)
        - configuration: 环节配置 (可选)
        """
        try:
            # 获取房间
            try:
                room = Room.objects.select_related('host').get(room_code=room_code)
            except Room.DoesNotExist:
                return Response({
                    "error": "房间不存在"
                }, status=status.HTTP_404_NOT_FOUND)

            # 验证房主权限
            if room.host != request.user:
                return Response({
                    "error": "只有房主可以添加环节"
                }, status=status.HTTP_403_FORBIDDEN)

            # 验证房间状态 - 只有READY状态的房间可以添加环节
            # READY状态表示房间在大厅，房主可以管理环节
            if room.status != RoomState.READY:
                return Response({
                    "error": f"房间当前状态为 {room.get_status_display()}，只有在大厅状态（READY）下才能添加环节"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取请求参数
            step_type = request.data.get('step_type')
            name = request.data.get('name', '')
            duration = request.data.get('duration', 300)
            configuration = request.data.get('configuration', {})

            # 验证环节类型
            if not step_type:
                return Response({
                    "error": "必须指定环节类型"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 验证环节类型是否有效
            valid_step_types = [choice[0] for choice in EventStep.STEP_TYPE_CHOICES]
            if step_type not in valid_step_types:
                return Response({
                    "error": f"无效的环节类型: {step_type}",
                    "valid_types": valid_step_types
                }, status=status.HTTP_400_BAD_REQUEST)

            # 验证订阅权限（付费环节检查）
            if step_type in EventStep.PREMIUM_STEP_TYPES:
                user_subscription = request.user.subscription_level
                if user_subscription == User.SUBSCRIPTION_FREE:
                    return Response({
                        "error": f"环节类型 {step_type} 需要付费订阅",
                        "required_subscription": "Pro",
                        "upgrade_required": True
                    }, status=status.HTTP_403_FORBIDDEN)

            # 验证持续时间
            try:
                duration = int(duration)
                if duration < 30 or duration > 3600:  # 30秒到1小时
                    return Response({
                        "error": "环节持续时间必须在30秒到3600秒之间"
                    }, status=status.HTTP_400_BAD_REQUEST)
            except (ValueError, TypeError):
                return Response({
                    "error": "无效的持续时间格式"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 创建新环节 - 使用新的RoomEventStep模型
            # 获取下一个order值
            from .models import RoomEventStep

            # 获取房间中最大的order值
            max_order = room.event_steps.aggregate(
                max_order=models.Max('order')
            )['max_order'] or 0
            next_order = max_order + 1

            # 获取环节类型的显示名称
            step_type_display = dict(EventStep.STEP_TYPE_CHOICES).get(step_type, step_type)

            # 创建新的房间环节
            new_step = RoomEventStep.objects.create(
                room=room,
                name=name or f"动态添加的{step_type_display}",
                order=next_order,
                step_type=step_type,
                duration=duration,
                configuration=configuration
            )

            logger.info(f"为房间 {room_code} 添加了新环节: {new_step}")

            # 通过WebSocket广播新环节添加信息
            from channels.layers import get_channel_layer
            from asgiref.sync import async_to_sync

            channel_layer = get_channel_layer()
            if channel_layer:
                try:
                    # 广播环节添加成功消息
                    async_to_sync(channel_layer.group_send)(
                        f"room_{room_code}",
                        {
                            'type': 'broadcast_step_added',
                            'payload': {
                                'message': f'房主添加了新环节：{new_step.name}',
                                'step': {
                                    'id': new_step.id,
                                    'name': new_step.name,
                                    'order': new_step.order,
                                    'step_type': new_step.step_type,
                                    'duration': new_step.duration,
                                    'configuration': new_step.configuration
                                },
                                'room_status': room.status,
                                'total_steps': room.event_steps.count()
                            }
                        }
                    )

                    # 广播房间条件状态更新，以便更新按钮显示
                    from core.services.room_manager import room_manager
                    condition_status = room_manager.get_room_condition_status_sync(room_code)
                    async_to_sync(channel_layer.group_send)(
                        f"room_{room_code}",
                        {
                            'type': 'broadcast_room_condition_update',
                            'payload': condition_status
                        }
                    )

                    logger.info(f"已广播环节添加消息到房间 {room_code}")
                except Exception as e:
                    logger.error(f"广播环节添加消息失败: {e}", exc_info=True)

            return Response({
                "message": "环节添加成功",
                "step": {
                    "id": new_step.id,
                    "name": new_step.name,
                    "order": new_step.order,
                    "step_type": new_step.step_type,
                    "duration": new_step.duration,
                    "configuration": new_step.configuration
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"添加环节时发生错误: {e}, 房间: {room_code}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "添加环节时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class InsertStepToRoomView(APIView):
    """
    插入环节到房间指定位置的API端点

    支持在房间的任意位置插入新环节，自动调整后续环节的顺序
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, room_code, *args, **kwargs):
        """
        在指定位置插入新环节

        请求体参数：
        - step_type: 环节类型 (GAME_PICTIONARY, FREE_CHAT, 等)
        - insert_order: 插入位置的order值
        - name: 环节名称 (可选)
        - duration: 环节持续时间，秒 (可选，默认300)
        - configuration: 环节配置 (可选)
        """
        try:
            # 获取房间
            try:
                room = Room.objects.select_related('host').get(room_code=room_code)
            except Room.DoesNotExist:
                return Response({
                    "error": "房间不存在"
                }, status=status.HTTP_404_NOT_FOUND)

            # 验证房主权限
            if room.host != request.user:
                return Response({
                    "error": "只有房主可以插入环节"
                }, status=status.HTTP_403_FORBIDDEN)

            # 验证房间状态
            if room.status != RoomState.READY:
                return Response({
                    "error": f"房间当前状态为 {room.get_status_display()}，只有在大厅状态（READY）下才能插入环节"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取请求参数
            step_type = request.data.get('step_type')
            insert_order = request.data.get('insert_order')
            name = request.data.get('name', '')
            duration = request.data.get('duration', 300)
            configuration = request.data.get('configuration', {})

            # 验证必要参数
            if not step_type or insert_order is None:
                return Response({
                    "error": "必须指定环节类型和插入位置"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 验证插入位置
            try:
                insert_order = int(insert_order)
                if insert_order <= room.current_step_order:
                    return Response({
                        "error": "不能在已完成的环节之前插入新环节"
                    }, status=status.HTTP_400_BAD_REQUEST)
            except (ValueError, TypeError):
                return Response({
                    "error": "无效的插入位置格式"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 验证环节类型
            valid_step_types = [choice[0] for choice in EventStep.STEP_TYPE_CHOICES]
            if step_type not in valid_step_types:
                return Response({
                    "error": f"无效的环节类型: {step_type}",
                    "valid_types": valid_step_types
                }, status=status.HTTP_400_BAD_REQUEST)

            # 验证订阅权限
            if step_type in EventStep.PREMIUM_STEP_TYPES:
                if request.user.subscription_level == User.SUBSCRIPTION_FREE:
                    return Response({
                        "error": f"环节类型 {step_type} 需要付费订阅",
                        "required_subscription": "Pro",
                        "upgrade_required": True
                    }, status=status.HTTP_403_FORBIDDEN)

            # 验证持续时间
            try:
                duration = int(duration)
                if duration < 30 or duration > 3600:
                    return Response({
                        "error": "环节持续时间必须在30秒到3600秒之间"
                    }, status=status.HTTP_400_BAD_REQUEST)
            except (ValueError, TypeError):
                return Response({
                    "error": "无效的持续时间格式"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 使用事务确保数据一致性
            from django.db import transaction
            from .models import RoomEventStep

            with transaction.atomic():
                # 将所有order大于或等于插入位置的环节的order值加1
                room.event_steps.filter(order__gte=insert_order).update(
                    order=models.F('order') + 1
                )

                # 获取环节类型的显示名称
                step_type_display = dict(EventStep.STEP_TYPE_CHOICES).get(step_type, step_type)

                # 创建新环节
                new_step = RoomEventStep.objects.create(
                    room=room,
                    name=name or f"插入的{step_type_display}",
                    order=insert_order,
                    step_type=step_type,
                    duration=duration,
                    configuration=configuration
                )

                logger.info(f"在房间 {room_code} 的位置 {insert_order} 插入了新环节: {new_step}")

            return Response({
                "message": "环节插入成功",
                "step": {
                    "id": new_step.id,
                    "name": new_step.name,
                    "order": new_step.order,
                    "step_type": new_step.step_type,
                    "duration": new_step.duration,
                    "configuration": new_step.configuration
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"插入环节时发生错误: {e}, 房间: {room_code}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "插入环节时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DeleteStepFromRoomView(APIView):
    """
    删除房间环节的API端点

    支持删除指定的房间环节，自动调整后续环节的顺序
    """
    permission_classes = [IsAuthenticated]

    def delete(self, request, room_code, step_id, *args, **kwargs):
        """
        删除指定的房间环节

        路径参数：
        - room_code: 房间代码
        - step_id: 要删除的环节ID
        """
        try:
            # 获取房间
            try:
                room = Room.objects.select_related('host').get(room_code=room_code)
            except Room.DoesNotExist:
                return Response({
                    "error": "房间不存在"
                }, status=status.HTTP_404_NOT_FOUND)

            # 验证房主权限
            if room.host != request.user:
                return Response({
                    "error": "只有房主可以删除环节"
                }, status=status.HTTP_403_FORBIDDEN)

            # 验证房间状态
            if room.status != RoomState.READY:
                return Response({
                    "error": f"房间当前状态为 {room.get_status_display()}，只有在大厅状态（READY）下才能删除环节"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取要删除的环节
            try:
                from .models import RoomEventStep
                step_to_delete = RoomEventStep.objects.get(
                    id=step_id,
                    room=room
                )
            except RoomEventStep.DoesNotExist:
                return Response({
                    "error": "环节不存在"
                }, status=status.HTTP_404_NOT_FOUND)

            # 验证不能删除已完成的环节
            if step_to_delete.order <= room.current_step_order:
                return Response({
                    "error": "不能删除已完成的环节"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 使用事务确保数据一致性
            from django.db import transaction

            with transaction.atomic():
                deleted_order = step_to_delete.order
                step_name = step_to_delete.name

                # 删除环节
                step_to_delete.delete()

                # 将所有order大于被删除环节的环节的order值减1
                room.event_steps.filter(order__gt=deleted_order).update(
                    order=models.F('order') - 1
                )

                logger.info(f"从房间 {room_code} 删除了环节: {step_name} (order: {deleted_order})")

            return Response({
                "message": "环节删除成功",
                "deleted_step": {
                    "id": step_id,
                    "name": step_name,
                    "order": deleted_order
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"删除环节时发生错误: {e}, 房间: {room_code}, 环节ID: {step_id}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "删除环节时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CheckInView(APIView):
    """
    用户签到API端点

    支持每日签到功能，包括签到状态查询和执行签到操作
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """获取用户签到状态"""
        try:
            from .models import CheckIn
            from django.utils import timezone

            user = request.user
            today = timezone.now().date()

            # 检查今天是否已签到
            has_checked_in_today = CheckIn.has_checked_in_today(user)

            # 获取连续签到天数
            consecutive_days = CheckIn.get_user_consecutive_days(user)

            # 获取总签到天数
            total_check_ins = CheckIn.get_user_total_check_ins(user)

            # 获取本月签到记录
            month_start = today.replace(day=1)
            if today.month == 12:
                month_end = today.replace(year=today.year + 1, month=1, day=1)
            else:
                month_end = today.replace(month=today.month + 1, day=1)

            month_check_ins = CheckIn.objects.filter(
                user=user,
                check_in_date__gte=month_start,
                check_in_date__lt=month_end,
                check_in_type=CheckIn.CHECK_IN_TYPE_DAILY
            ).values_list('check_in_date', flat=True)

            # 转换为字符串列表
            month_check_in_dates = [date.isoformat() for date in month_check_ins]

            return Response({
                'has_checked_in_today': has_checked_in_today,
                'consecutive_days': consecutive_days,
                'total_check_ins': total_check_ins,
                'month_check_in_dates': month_check_in_dates,
                'today': today.isoformat()
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取签到状态失败: {e}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "获取签到状态时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request, *args, **kwargs):
        """执行签到操作"""
        try:
            from .models import CheckIn
            from django.utils import timezone
            from django.db import transaction

            user = request.user
            today = timezone.now().date()

            # 检查今天是否已签到
            if CheckIn.has_checked_in_today(user):
                return Response({
                    "error": "今天已经签到过了",
                    "message": "每天只能签到一次"
                }, status=status.HTTP_400_BAD_REQUEST)

            # 执行签到
            with transaction.atomic():
                # 计算连续签到天数
                consecutive_days = CheckIn.get_user_consecutive_days(user) + 1

                # 创建签到记录
                check_in = CheckIn.objects.create(
                    user=user,
                    check_in_date=today,
                    check_in_type=CheckIn.CHECK_IN_TYPE_DAILY,
                    consecutive_days=consecutive_days,
                    reward_points=1  # 基础奖励1分
                )

                logger.info(f"用户 {user.username} 签到成功，连续签到 {consecutive_days} 天")

                return Response({
                    "message": "签到成功！",
                    "check_in_date": check_in.check_in_date.isoformat(),
                    "consecutive_days": consecutive_days,
                    "reward_points": check_in.reward_points,
                    "total_check_ins": CheckIn.get_user_total_check_ins(user)
                }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"签到失败: {e}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "签到时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RoomStepsView(APIView):
    """
    房间环节列表API端点

    提供房间环节的查询功能，支持时间轴显示
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, room_code, *args, **kwargs):
        """获取房间环节列表"""
        try:
            # 获取房间
            try:
                room = Room.objects.select_related('host').get(room_code=room_code)
            except Room.DoesNotExist:
                return Response({
                    "error": "房间不存在"
                }, status=status.HTTP_404_NOT_FOUND)

            # 检查用户是否在房间中
            from .models import RoomParticipant
            if not RoomParticipant.objects.filter(room=room, user=request.user, left_at__isnull=True).exists():
                return Response({
                    "error": "您不在此房间中"
                }, status=status.HTTP_403_FORBIDDEN)

            # 获取房间环节列表
            steps = room.event_steps.all().order_by('order')

            steps_data = []
            for step in steps:
                steps_data.append({
                    'id': step.id,
                    'order': step.order,
                    'name': step.name,
                    'step_type': step.step_type,
                    'duration': step.duration,
                    'configuration': step.configuration,
                    'is_completed': step.is_completed,
                    'completed_at': step.completed_at.isoformat() if step.completed_at else None,
                    'is_current': room.current_event_step_id == step.id if room.current_event_step else False
                })

            return Response({
                'room_code': room_code,
                'room_status': room.status,
                'current_step_order': room.current_step_order,
                'total_steps': len(steps_data),
                'steps': steps_data,
                'is_host': room.host == request.user
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取房间环节列表失败: {e}, 房间: {room_code}, 用户: {request.user.username}", exc_info=True)
            return Response({
                "error": "获取房间环节列表时发生内部错误",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
