#!/usr/bin/env python3
"""
WebSocket协议测试脚本
验证前后端对接清单中的WebSocket消息协议是否正确工作
"""

import asyncio
import websockets
import json
import sys

async def test_websocket_protocol():
    """测试WebSocket协议"""
    
    print("🔍 测试WebSocket协议...")
    
    # 这里我们只测试连接和基本消息格式
    # 实际的房间功能需要完整的认证流程
    
    try:
        # 尝试连接到WebSocket（这会失败，因为需要认证，但我们可以检查错误类型）
        uri = "ws://localhost:8000/ws/room/TEST123/?token=dummy_token"
        print(f"尝试连接到: {uri}")
        
        try:
            async with websockets.connect(uri) as websocket:
                print("✅ WebSocket连接成功")
                
                # 测试心跳消息格式
                heartbeat_msg = {
                    "action": "heartbeat",
                    "payload": {}
                }
                
                print("📤 发送心跳消息:", json.dumps(heartbeat_msg))
                await websocket.send(json.dumps(heartbeat_msg))
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print("📥 收到响应:", response)
                    
                    # 解析响应
                    try:
                        data = json.loads(response)
                        if data.get('type') == 'heartbeat_response':
                            print("✅ 心跳响应格式正确")
                        else:
                            print(f"📋 收到其他消息类型: {data.get('type')}")
                    except json.JSONDecodeError:
                        print("❌ 响应不是有效的JSON")
                        
                except asyncio.TimeoutError:
                    print("⏰ 等待响应超时")
                    
        except websockets.exceptions.ConnectionClosedError as e:
            print(f"🔒 连接被关闭: {e.code} - {e.reason}")
            if e.code == 4001:
                print("✅ 认证失败关闭码正确 (4001)")
            elif e.code == 4005:
                print("✅ 会话超时关闭码正确 (4005)")
            else:
                print(f"📋 其他关闭码: {e.code}")
                
        except websockets.exceptions.ConnectionClosedOK:
            print("✅ 连接正常关闭")
            
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")
    
    print("\n🎯 WebSocket协议测试完成！")
    print("注意：由于需要有效的认证token，连接可能会被拒绝，这是正常的")

def test_message_formats():
    """测试消息格式"""
    print("\n📋 测试消息格式...")
    
    # 测试前端发送格式
    frontend_messages = [
        {"action": "heartbeat", "payload": {}},
        {"action": "send_message", "payload": {"message": "Hello"}},
        {"action": "send_drawing", "payload": {"path_data": {"id": "1", "path": "M10,10 L20,20", "color": "#000"}}},
        {"action": "guess_word", "payload": {"guess": "apple"}},
        {"action": "next_step", "payload": {}},
        {"action": "restart_game", "payload": {"game_type": "PICTIONARY"}},
        {"action": "return_to_lobby", "payload": {}}
    ]
    
    print("✅ 前端发送消息格式:")
    for msg in frontend_messages:
        print(f"   {json.dumps(msg)}")
    
    # 测试后端推送格式
    backend_messages = [
        {"type": "step_started", "payload": {"step": {"step_type": "GAME_PICTIONARY"}}},
        {"type": "round_over", "payload": {"winner": "player1", "word": "apple"}},
        {"type": "participants_update", "payload": [{"username": "player1", "is_host": True}]},
        {"type": "room_state_update", "payload": {"room_status": "IN_PROGRESS"}},
        {"type": "session_timeout", "payload": {"message": "Session expired"}},
        {"type": "heartbeat_response", "payload": {"timestamp": "2025-07-22T10:00:00Z"}}
    ]
    
    print("\n✅ 后端推送消息格式:")
    for msg in backend_messages:
        print(f"   {json.dumps(msg)}")
    
    print("\n🎯 消息格式验证完成！")

if __name__ == "__main__":
    print("🚀 开始WebSocket协议测试...\n")
    
    # 测试消息格式
    test_message_formats()
    
    # 测试WebSocket连接
    try:
        asyncio.run(test_websocket_protocol())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
